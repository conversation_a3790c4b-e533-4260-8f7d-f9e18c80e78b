// lib/services/bybit_api.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import '../models/position.dart';

class BybitApiService {
  static const String baseUrl = 'https://api.bybit.com';
  static const String testnetUrl = 'https://api-testnet.bybit.com';
  
  final String apiKey;
  final String apiSecret;
  final bool isTestnet;

  BybitApiService({
    required this.apiKey,
    required this.apiSecret,
    this.isTestnet = false,
  });

  String get apiUrl => isTestnet ? testnetUrl : baseUrl;

  // API 서명 생성
  String _generateSignature(int timestamp, String queryString) {
    final signString = '$timestamp$apiKey$queryString';
    final hmac = Hmac(sha256, utf8.encode(apiSecret));
    final digest = hmac.convert(utf8.encode(signString));
    return digest.toString();
  }

  // HTTP 헤더 생성
  Map<String, String> _getHeaders(String queryString) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final signature = _generateSignature(timestamp, queryString);
    
    return {
      'X-BAPI-API-KEY': apiKey,
      'X-BAPI-TIMESTAMP': timestamp.toString(),
      'X-BAPI-SIGN': signature,
      'Content-Type': 'application/json',
    };
  }

  // 지갑 잔고 조회
  Future<double> getWalletBalance() async {
    const endpoint = '/v5/account/wallet-balance';
    final params = {'accountType': 'UNIFIED', 'coin': 'USDT'};
    final queryString = Uri(queryParameters: params).query;
    
    final response = await http.get(
      Uri.parse('$apiUrl$endpoint?$queryString'),
      headers: _getHeaders(queryString),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['retCode'] == 0) {
        final walletBalance = data['result']['list'][0]['coin'][0]['walletBalance'];
        return double.parse(walletBalance);
      }
    }
    
    throw Exception('Failed to get wallet balance');
  }

  // 현재가 조회
  Future<double> getCurrentPrice(String symbol) async {
    const endpoint = '/v5/market/tickers';
    final params = {'category': 'linear', 'symbol': symbol};
    final queryString = Uri(queryParameters: params).query;
    
    final response = await http.get(
      Uri.parse('$apiUrl$endpoint?$queryString'),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['retCode'] == 0) {
        final lastPrice = data['result']['list'][0]['lastPrice'];
        return double.parse(lastPrice);
      }
    }
    
    throw Exception('Failed to get current price');
  }

  // 포지션 조회
  Future<List<Position>> getPositions(String symbol) async {
    const endpoint = '/v5/position/list';
    final params = {
      'category': 'linear',
      'symbol': symbol,
      'settleCoin': 'USDT',
    };
    final queryString = Uri(queryParameters: params).query;
    
    final response = await http.get(
      Uri.parse('$apiUrl$endpoint?$queryString'),
      headers: _getHeaders(queryString),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['retCode'] == 0) {
        final List positions = data['result']['list'];
        return positions
            .where((p) => double.parse(p['size'].toString()) > 0)
            .map((p) => Position.fromBybitResponse(p))
            .toList();
      }
    }
    
    throw Exception('Failed to get positions');
  }

  // 레버리지 설정
  Future<void> setLeverage({
    required String symbol,
    required String leverage,
  }) async {
    const endpoint = '/v5/position/set-leverage';
    final body = {
      'category': 'linear',
      'symbol': symbol,
      'buyLeverage': leverage,
      'sellLeverage': leverage,
    };
    
    final response = await http.post(
      Uri.parse('$apiUrl$endpoint'),
      headers: _getHeaders(''),
      body: jsonEncode(body),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to set leverage');
    }
  }

  // 지정가 주문
  Future<String> placeLimitOrder({
    required String symbol,
    required String side,
    required double qty,
    required double price,
    required String positionIdx,
  }) async {
    const endpoint = '/v5/order/create';
    final body = {
      'category': 'linear',
      'symbol': symbol,
      'side': side,
      'orderType': 'Limit',
      'qty': qty.toStringAsFixed(3),
      'price': price.toStringAsFixed(2),
      'positionIdx': int.parse(positionIdx),
      'timeInForce': 'GTC',
    };
    
    final response = await http.post(
      Uri.parse('$apiUrl$endpoint'),
      headers: _getHeaders(''),
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['retCode'] == 0) {
        return data['result']['orderId'];
      }
    }
    
    throw Exception('Failed to place limit order');
  }

  // 시장가 주문 (청산용)
  Future<String> placeMarketOrder({
    required String symbol,
    required String side,
    required double qty,
    required String positionIdx,
  }) async {
    const endpoint = '/v5/order/create';
    final body = {
      'category': 'linear',
      'symbol': symbol,
      'side': side,
      'orderType': 'Market',
      'qty': qty.toStringAsFixed(3),
      'positionIdx': int.parse(positionIdx),
      'timeInForce': 'IOC',
      'reduceOnly': true,
    };
    
    final response = await http.post(
      Uri.parse('$apiUrl$endpoint'),
      headers: _getHeaders(''),
      body: jsonEncode(body),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['retCode'] == 0) {
        return data['result']['orderId'];
      }
    }
    
    throw Exception('Failed to place market order');
  }

  // 주문 취소
  Future<void> cancelOrder({
    required String symbol,
    required String orderId,
  }) async {
    const endpoint = '/v5/order/cancel';
    final body = {
      'category': 'linear',
      'symbol': symbol,
      'orderId': orderId,
    };
    
    final response = await http.post(
      Uri.parse('$apiUrl$endpoint'),
      headers: _getHeaders(''),
      body: jsonEncode(body),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to cancel order');
    }
  }
}