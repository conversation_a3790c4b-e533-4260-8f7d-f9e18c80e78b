// lib/widgets/inputs.dart
import 'package:flutter/material.dart';
import '../constants.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final IconData icon;
  final bool obscureText;
  final TextInputType? keyboardType;
  final bool enabled;

  const CustomTextField({
    super.key,
    required this.controller,
    required this.label,
    required this.icon,
    this.obscureText = false,
    this.keyboardType,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      enabled: enabled,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: Colors.grey[400]),
        prefixIcon: Icon(icon, color: AppConstants.primaryColor),
        filled: true,
        fillColor: AppConstants.cardColor.withValues(alpha: 0.5 * 255),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppConstants.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppConstants.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppConstants.primaryColor.withValues(alpha: 0.5 * 255),
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Colors.grey.withValues(alpha: 0.3 * 255),
          ),
        ),
      ),
    );
  }
}

class CustomDropdown<T> extends StatelessWidget {
  final String label;
  final T value;
  final List<T> items;
  final Function(T?) onChanged;
  final IconData icon;
  final String Function(T)? itemBuilder;
  final bool enabled;

  const CustomDropdown({
    super.key,
    required this.label,
    required this.value,
    required this.items,
    required this.onChanged,
    required this.icon,
    this.itemBuilder,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[300],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: AppConstants.cardColor.withValues(alpha: 0.5 * 255),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppConstants.borderColor,
            ),
          ),
          child: DropdownButton<T>(
            value: value,
            isExpanded: true,
            dropdownColor: AppConstants.cardColor,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            underline: const SizedBox(),
            icon: Icon(
              Icons.arrow_drop_down,
              color: enabled ? Colors.white : Colors.grey,
            ),
            onChanged: enabled ? onChanged : null,
            items: items.map((item) {
              final displayText =
                  itemBuilder != null ? itemBuilder!(item) : item.toString();

              return DropdownMenuItem<T>(
                value: item,
                child: Row(
                  children: [
                    Icon(icon, color: AppConstants.primaryColor, size: 20),
                    const SizedBox(width: 12),
                    Text(displayText),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

class CustomSwitch extends StatelessWidget {
  final String label;
  final bool value;
  final Function(bool) onChanged;
  final bool enabled;

  const CustomSwitch({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        Switch(
          value: value,
          onChanged: enabled ? onChanged : null,
          activeColor: AppConstants.primaryColor,
          activeTrackColor:
              AppConstants.primaryColor.withValues(alpha: 0.3 * 255),
          inactiveThumbColor: Colors.grey,
          inactiveTrackColor: Colors.grey.withValues(alpha: 0.3 * 255),
        ),
      ],
    );
  }
}
