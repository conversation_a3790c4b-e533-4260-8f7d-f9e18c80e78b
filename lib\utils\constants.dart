// lib/utils/constants.dart
import 'package:flutter/material.dart';

class AppConstants {
  // 색상
  static const Color backgroundColor = Color(0xFF242424);
  static const Color cardColor = Color(0xFF303030);
  static const Color primaryColor = Color(0xFFFF4833);
  static const Color borderColor = Color(0xFF606060);

  // 심볼
  static const List<String> symbols = [
    'BTCUSDT',
    'ETHUSDT',
    'XRPUSDT',
    'SOLUSDT',
    'WLDUSDT',
    'DOGEUSDT',
  ];

  // 레버리지 옵션
  static const List<int> leverageOptions = [5, 10, 15, 20, 25, 30, 50];

  // 투자 비율 옵션
  static const List<int> investmentRatios = [25, 50, 75, 100];

  // 테마
  static final ThemeData darkTheme = ThemeData.dark().copyWith(
    scaffoldBackgroundColor: backgroundColor,
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.dark(
      primary: primaryColor,
      secondary: primaryColor,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor.withValues(alpha: 0.08),
        foregroundColor: primaryColor,
      ),
    ),
  );

  // 뉴모피즘 효과
  static final BoxShadow innerNeumorphism = BoxShadow(
    color: Colors.black.withValues(alpha: 0.15),
    offset: const Offset(2, 2),
    blurRadius: 4,
    spreadRadius: -2,
  );

  static final BoxShadow subtleNeumorphism = BoxShadow(
    color: Colors.black.withValues(alpha: 0.2),
    offset: const Offset(3, 3),
    blurRadius: 6,
  );
}
