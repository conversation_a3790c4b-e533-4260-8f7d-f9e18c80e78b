// lib/models/signal.dart
class Signal {
  final String id;
  final String type; // 'long' or 'short'
  final DateTime timestamp;
  final String source; // 'manual', 'auto', 'pipe'
  final Map<String, dynamic> metadata;

  Signal({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.source,
    this.metadata = const {},
  });

  factory Signal.fromJson(Map<String, dynamic> json) {
    return Signal(
      id: json['id'],
      type: json['type'],
      timestamp: DateTime.parse(json['timestamp']),
      source: json['source'],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type,
        'timestamp': timestamp.toIso8601String(),
        'source': source,
        'metadata': metadata,
      };
}
