// lib/screens/login.dart
import 'package:flutter/material.dart';
import '../services.dart';
import '../constants.dart';
import '../widgets.dart';
import 'dashboard.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();

  final DatabaseService _db = DatabaseService();

  bool _isLoading = false;
  bool _isSignUp = false;

  @override
  void initState() {
    super.initState();
    _initDatabase();
  }

  Future<void> _initDatabase() async {
    await _db.init();

    // 자동 로그인 체크
    final currentUser = await _db.getCurrentUser();
    if (currentUser != null && mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => DashboardScreen(user: currentUser),
        ),
      );
    }
  }

  Future<void> _handleLogin() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      _showSnackBar('모든 필드를 입력해주세요');
      return;
    }

    setState(() => _isLoading = true);

    try {
      print('🔐 로그인 시도: ${_emailController.text}');

      // 데이터베이스 서비스의 authenticateUser 메서드 사용
      final user = await _db.authenticateUser(
          _emailController.text, _passwordController.text);

      if (user != null) {
        print('✅ 로그인 성공: ${user.name}');

        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => DashboardScreen(user: user),
            ),
          );
        }
      } else {
        print('❌ 로그인 실패');
        _showSnackBar('이메일 또는 비밀번호가 올바르지 않습니다');
      }
    } catch (e) {
      print('❌ 로그인 오류: $e');
      _showSnackBar('로그인 중 오류가 발생했습니다: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleSignUp() async {
    if (_emailController.text.isEmpty ||
        _passwordController.text.isEmpty ||
        _nameController.text.isEmpty) {
      _showSnackBar('모든 필드를 입력해주세요');
      return;
    }

    setState(() => _isLoading = true);

    try {
      // 이메일 중복 체크
      final existingUser = await _db.getUser(_emailController.text);
      if (existingUser != null) {
        _showSnackBar('이미 존재하는 이메일입니다');
        return;
      }

      // 새 사용자 등록 (데이터베이스 서비스 사용)
      final success = await _db.registerUser(
        email: _emailController.text,
        name: _nameController.text,
        password: _passwordController.text,
        isAdmin: false,
      );

      if (!success) {
        _showSnackBar('회원가입에 실패했습니다');
        return;
      }

      _showSnackBar('회원가입이 완료되었습니다');
      setState(() => _isSignUp = false);

      // 입력 필드 초기화
      _nameController.clear();
      _passwordController.clear();
    } catch (e) {
      _showSnackBar('회원가입 중 오류가 발생했습니다');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: GlassContainer(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 로고
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor
                          .withValues(alpha: 0.08 * 255),
                      borderRadius: BorderRadius.circular(50),
                      border: Border.all(
                        color: AppConstants.primaryColor
                            .withValues(alpha: 0.3 * 255),
                      ),
                    ),
                    child: const Icon(
                      Icons.currency_bitcoin,
                      size: 50,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // 타이틀
                  const Text(
                    'Open Systems_Bot',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '바이비트 자동매매 시스템',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[400],
                    ),
                  ),
                  const SizedBox(height: 32),

                  // 입력 필드
                  if (_isSignUp) ...[
                    CustomTextField(
                      controller: _nameController,
                      label: '이름',
                      icon: Icons.person,
                    ),
                    const SizedBox(height: 16),
                  ],

                  CustomTextField(
                    controller: _emailController,
                    label: '이메일',
                    icon: Icons.email,
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),

                  CustomTextField(
                    controller: _passwordController,
                    label: '비밀번호',
                    icon: Icons.lock,
                    obscureText: true,
                  ),
                  const SizedBox(height: 24),

                  // 로그인/회원가입 버튼
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isLoading
                          ? null
                          : (_isSignUp ? _handleSignUp : _handleLogin),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConstants.primaryColor
                            .withValues(alpha: 0.08 * 255),
                        foregroundColor: AppConstants.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: AppConstants.primaryColor
                                .withValues(alpha: 0.3 * 255),
                          ),
                        ),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : Text(
                              _isSignUp ? '회원가입' : '로그인',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 모드 전환
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _isSignUp = !_isSignUp;
                      });
                    },
                    child: Text(
                      _isSignUp ? '이미 계정이 있으신가요? 로그인' : '계정이 없으신가요? 회원가입',
                      style: TextStyle(
                        color: Colors.grey[400],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
