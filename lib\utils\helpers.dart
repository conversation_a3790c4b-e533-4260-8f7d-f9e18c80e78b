// lib/utils/helpers.dart
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class Helpers {
  // 숫자 포맷터
  static String formatNumber(double number, {int decimals = 2}) {
    final formatter = NumberFormat('#,##0.${'0' * decimals}');
    return formatter.format(number);
  }

  // 퍼센트 포맷터
  static String formatPercent(double percent, {bool showSign = true}) {
    final sign = showSign && percent >= 0 ? '+' : '';
    return '$sign${percent.toStringAsFixed(2)}%';
  }

  // 시간 포맷터
  static String formatTime(DateTime time) {
    return DateFormat('HH:mm:ss').format(time);
  }

  // 날짜 포맷터
  static String formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // 날짜시간 포맷터
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  // 심볼 아이콘 텍스트 반환
  static String getSymbolIcon(String symbol) {
    switch (symbol) {
      case 'BTCUSDT':
        return '₿';
      case 'ETHUSDT':
        return 'Ξ';
      case 'XRPUSDT':
        return 'ꭆ';
      case 'SOLUSDT':
        return '◎';
      case 'WLDUSDT':
        return '⬡';
      case 'DOGEUSDT':
        return 'Ð';
      default:
        return '₿';
    }
  }

  // 색상 헬퍼
  static Color getSignalColor(String type) {
    switch (type) {
      case 'long':
        return const Color(0xFF4CAF50);
      case 'short':
        return const Color(0xFFF44336);
      default:
        return Colors.grey;
    }
  }

  // 로그 레벨 색상
  static Color getLogLevelColor(String level) {
    switch (level) {
      case 'info':
        return Colors.blue;
      case 'warning':
        return Colors.orange;
      case 'error':
        return Colors.red;
      case 'success':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
