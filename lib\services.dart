// lib/services.dart
import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'models.dart';

// DatabaseService
class DatabaseService {
  late SharedPreferences _prefs;

  // JSON 키
  static const String _usersKey = 'users';
  static const String _settingsKey = 'settings';
  static const String _tradesKey = 'trades';
  static const String _signalsKey = 'signals';
  static const String _logsKey = 'logs';
  static const String _currentUserKey = 'currentUser';

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _initializeDefaults();
  }

  Future<void> _initializeDefaults() async {
    // 관리자 계정 초기화
    if (!_prefs.containsKey(_usersKey)) {
      final adminUser = User(
        email: '<EMAIL>',
        name: '관리자',
        passwordHash: _hashPassword('admin123'),
        isAdmin: true,
        createdAt: DateTime.now(),
      );

      await saveUser(adminUser);
    }

    // 기본 설정 초기화
    if (!_prefs.containsKey(_settingsKey)) {
      await saveSettings(Settings());
    }
  }

  // 비밀번호 해싱 (간단한 구현, 실제로는 bcrypt 사용)
  String _hashPassword(String password) {
    // TODO: 실제 구현시 bcrypt 사용
    return base64.encode(utf8.encode(password));
  }

  // 사용자 관련
  Future<void> saveUser(User user) async {
    final users = await getAllUsers();
    users[user.email] = user;

    final jsonString =
        jsonEncode(users.map((key, value) => MapEntry(key, value.toJson())));
    await _prefs.setString(_usersKey, jsonString);
  }

  Future<Map<String, User>> getAllUsers() async {
    final jsonString = _prefs.getString(_usersKey);
    if (jsonString == null) return {};

    final Map<String, dynamic> json = jsonDecode(jsonString);
    return json.map((key, value) => MapEntry(key, User.fromJson(value)));
  }

  Future<User?> getUser(String email) async {
    final users = await getAllUsers();
    return users[email];
  }

  Future<User?> getCurrentUser() async {
    final email = _prefs.getString(_currentUserKey);
    if (email == null) return null;
    return getUser(email);
  }

  Future<void> setCurrentUser(String email) async {
    await _prefs.setString(_currentUserKey, email);
  }

  Future<void> clearCurrentUser() async {
    await _prefs.remove(_currentUserKey);
  }

  // 설정 관련
  Future<void> saveSettings(Settings settings) async {
    await _prefs.setString(_settingsKey, jsonEncode(settings.toJson()));
  }

  Future<Settings> getSettings() async {
    final jsonString = _prefs.getString(_settingsKey);
    if (jsonString == null) return Settings();
    return Settings.fromJson(jsonDecode(jsonString));
  }

  // 신호 관련
  Future<void> saveSignal(Signal signal) async {
    final signals = await getAllSignals();
    signals.add(signal);

    // 최근 100개만 유지
    if (signals.length > 100) {
      signals.removeRange(0, signals.length - 100);
    }

    final jsonString = jsonEncode(signals.map((s) => s.toJson()).toList());
    await _prefs.setString(_signalsKey, jsonString);
  }

  Future<List<Signal>> getAllSignals() async {
    final jsonString = _prefs.getString(_signalsKey);
    if (jsonString == null) return [];

    final List<dynamic> json = jsonDecode(jsonString);
    return json.map((item) => Signal.fromJson(item)).toList();
  }

  // 거래 기록
  Future<void> saveTrade(Map<String, dynamic> trade) async {
    final trades = await getAllTrades();
    trades.add(trade);

    // 최근 1000개만 유지
    if (trades.length > 1000) {
      trades.removeRange(0, trades.length - 1000);
    }

    await _prefs.setString(_tradesKey, jsonEncode(trades));
  }

  Future<List<Map<String, dynamic>>> getAllTrades() async {
    final jsonString = _prefs.getString(_tradesKey);
    if (jsonString == null) return [];

    final List<dynamic> json = jsonDecode(jsonString);
    return json.cast<Map<String, dynamic>>();
  }

  // 로그
  Future<void> saveLog(Map<String, dynamic> log) async {
    final logs = await getAllLogs();
    logs.add(log);

    // 최근 500개만 유지
    if (logs.length > 500) {
      logs.removeRange(0, logs.length - 500);
    }

    await _prefs.setString(_logsKey, jsonEncode(logs));
  }

  Future<List<Map<String, dynamic>>> getAllLogs() async {
    final jsonString = _prefs.getString(_logsKey);
    if (jsonString == null) return [];

    final List<dynamic> json = jsonDecode(jsonString);
    return json.cast<Map<String, dynamic>>();
  }

  // 전체 데이터 초기화
  Future<void> clearAllData() async {
    await _prefs.clear();
    await _initializeDefaults();
  }
}

// CryptoService
class CryptoService {
  // XOR 암호화 키 (웹 호환성을 위해 간단한 구현)
  static const String _encryptionKey = 'OpenSystemsBot2024SecureKey12345';

  // XOR 암호화 (웹 호환성을 위해)
  static String _xorEncode(String input, String key) {
    final inputBytes = utf8.encode(input);
    final keyBytes = utf8.encode(key);
    final result = <int>[];

    for (int i = 0; i < inputBytes.length; i++) {
      result.add(inputBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64.encode(result);
  }

  static String _xorDecode(String encoded, String key) {
    final encodedBytes = base64.decode(encoded);
    final keyBytes = utf8.encode(key);
    final result = <int>[];

    for (int i = 0; i < encodedBytes.length; i++) {
      result.add(encodedBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return utf8.decode(result);
  }

  // 문자열 암호화
  static String encryptString(String plainText) {
    return _xorEncode(plainText, _encryptionKey);
  }

  // 문자열 복호화
  static String decryptString(String encryptedText) {
    try {
      return _xorDecode(encryptedText, _encryptionKey);
    } catch (e) {
      return '';
    }
  }

  // 비밀번호 해싱
  static String hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 비밀번호 검증
  static bool verifyPassword(String password, String hash) {
    return hashPassword(password) == hash;
  }

  // API 키 암호화
  static Map<String, String> encryptApiKeys({
    required String apiKey,
    required String apiSecret,
  }) {
    return {
      'apiKey': encryptString(apiKey),
      'apiSecret': encryptString(apiSecret),
    };
  }

  // API 키 복호화
  static Map<String, String> decryptApiKeys({
    required String encryptedApiKey,
    required String encryptedApiSecret,
  }) {
    if (encryptedApiKey.isEmpty || encryptedApiSecret.isEmpty) {
      return {'apiKey': '', 'apiSecret': ''};
    }

    return {
      'apiKey': decryptString(encryptedApiKey),
      'apiSecret': decryptString(encryptedApiSecret),
    };
  }
}

// PipeListener
class PipeListener {
  WebSocketChannel? _channel;
  final _signalController = StreamController<Signal>.broadcast();

  Stream<Signal> get signalStream => _signalController.stream;

  bool _isConnected = false;
  String? _lastProcessedSignalId;
  Timer? _reconnectTimer;

  bool get isConnected => _isConnected;

  // WebSocket 서버에 연결
  Future<void> connect() async {
    try {
      print('Named Pipe 서버에 연결 시도 중...');

      // WebSocket 채널 생성 (Flutter 웹에서는 Named Pipe 대신 WebSocket 사용)
      _channel = IOWebSocketChannel.connect('ws://localhost:8765');

      await _channel!.ready;

      _isConnected = true;
      print('Named Pipe 서버에 연결됨');

      // 메시지 수신 대기
      _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
    } catch (e) {
      print('Named Pipe 연결 실패: $e');
      _scheduleReconnect();
    }
  }

  void _handleMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String);

      // 중복 신호 방지
      if (data['id'] == _lastProcessedSignalId) {
        return;
      }

      final signal = Signal.fromJson(data);
      _lastProcessedSignalId = signal.id;

      print('신호 수신: ${signal.type} at ${signal.timestamp}');
      _signalController.add(signal);
    } catch (e) {
      print('신호 파싱 오류: $e');
    }
  }

  void _handleError(error) {
    print('Named Pipe 오류: $error');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _handleDisconnection() {
    print('Named Pipe 연결 끊어짐');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(const Duration(seconds: 5), () {
      if (!_isConnected) {
        print('Named Pipe 재연결 시도...');
        connect();
      }
    });
  }

  void dispose() {
    _reconnectTimer?.cancel();
    _channel?.sink.close();
    _signalController.close();
  }
}

// BybitApiService
class BybitApiService {
  final String apiKey;
  final String apiSecret;
  final bool isTestnet;
  final http.Client _client = http.Client();

  late String _baseUrl;

  BybitApiService({
    required this.apiKey,
    required this.apiSecret,
    this.isTestnet = true,
  }) {
    _baseUrl =
        isTestnet ? 'https://api-testnet.bybit.com' : 'https://api.bybit.com';
  }

  // HMAC SHA256 서명 생성
  String _generateSignature(String queryString) {
    final key = utf8.encode(apiSecret);
    final message = utf8.encode(queryString);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(message);
    return digest.toString();
  }

  // API 요청 헤더 생성
  Map<String, String> _createHeaders({
    required String timestamp,
    required String signature,
    String? recv_window,
  }) {
    return {
      'X-BAPI-API-KEY': apiKey,
      'X-BAPI-SIGN': signature,
      'X-BAPI-TIMESTAMP': timestamp,
      'X-BAPI-RECV-WINDOW': recv_window ?? '5000',
      'Content-Type': 'application/json',
    };
  }

  // GET 요청
  Future<Map<String, dynamic>> _get(String endpoint,
      [Map<String, String>? params]) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final recv_window = '5000';

    String queryString = 'timestamp=$timestamp&recv_window=$recv_window';
    if (params != null && params.isNotEmpty) {
      final paramString =
          params.entries.map((e) => '${e.key}=${e.value}').join('&');
      queryString = '$paramString&$queryString';
    }

    final signature = _generateSignature(queryString);
    final headers = _createHeaders(
      timestamp: timestamp,
      signature: signature,
      recv_window: recv_window,
    );

    final url = '$_baseUrl$endpoint?$queryString';
    final response = await _client.get(Uri.parse(url), headers: headers);

    return jsonDecode(response.body);
  }

  // POST 요청
  Future<Map<String, dynamic>> _post(
      String endpoint, Map<String, dynamic> body) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final recv_window = '5000';
    final bodyString = jsonEncode(body);

    final queryString =
        'timestamp=$timestamp&recv_window=$recv_window$bodyString';
    final signature = _generateSignature(queryString);

    final headers = _createHeaders(
      timestamp: timestamp,
      signature: signature,
      recv_window: recv_window,
    );

    final url = '$_baseUrl$endpoint';
    final response = await _client.post(
      Uri.parse(url),
      headers: headers,
      body: bodyString,
    );

    return jsonDecode(response.body);
  }

  // 지갑 잔고 조회
  Future<double> getWalletBalance() async {
    try {
      final response = await _get('/v5/account/wallet-balance', {
        'accountType': 'UNIFIED',
      });

      if (response['retCode'] == 0) {
        final result = response['result'];
        final coins = result['list'][0]['coin'] as List;
        final usdtCoin = coins.firstWhere(
          (coin) => coin['coin'] == 'USDT',
          orElse: () => {'walletBalance': '0'},
        );
        return double.parse(usdtCoin['walletBalance']);
      }

      return 0.0;
    } catch (e) {
      print('잔고 조회 오류: $e');
      return 0.0;
    }
  }

  // 포지션 조회
  Future<List<Position>> getPositions({String? symbol}) async {
    try {
      final params = <String, String>{
        'category': 'linear',
      };

      if (symbol != null) {
        params['symbol'] = symbol;
      }

      final response = await _get('/v5/position/list', params);

      if (response['retCode'] == 0) {
        final result = response['result'];
        final positions = result['list'] as List;

        return positions
            .where((pos) => double.parse(pos['size']) > 0)
            .map((pos) => Position.fromBybitResponse(pos))
            .toList();
      }

      return [];
    } catch (e) {
      print('포지션 조회 오류: $e');
      return [];
    }
  }

  // 주문 생성
  Future<bool> createOrder({
    required String symbol,
    required String side,
    required String orderType,
    required String qty,
    String? price,
    String? timeInForce,
  }) async {
    try {
      final body = {
        'category': 'linear',
        'symbol': symbol,
        'side': side,
        'orderType': orderType,
        'qty': qty,
        if (price != null) 'price': price,
        'timeInForce': timeInForce ?? 'GTC',
      };

      final response = await _post('/v5/order/create', body);

      return response['retCode'] == 0;
    } catch (e) {
      print('주문 생성 오류: $e');
      return false;
    }
  }

  // 레버리지 설정
  Future<bool> setLeverage({
    required String symbol,
    required String buyLeverage,
    required String sellLeverage,
  }) async {
    try {
      final body = {
        'category': 'linear',
        'symbol': symbol,
        'buyLeverage': buyLeverage,
        'sellLeverage': sellLeverage,
      };

      final response = await _post('/v5/position/set-leverage', body);

      return response['retCode'] == 0;
    } catch (e) {
      print('레버리지 설정 오류: $e');
      return false;
    }
  }

  void dispose() {
    _client.close();
  }
}

// TradingBot
class TradingBot {
  final DatabaseService _db = DatabaseService();
  final PipeListener _pipeListener = PipeListener();

  BybitApiService? _api;
  Timer? _positionCheckTimer;
  Timer? _balanceUpdateTimer;
  bool _isRunning = false;
  Settings? _settings;

  // 상태
  double _walletBalance = 0.0;
  List<Position> _currentPositions = [];
  Signal? _lastSignal;

  // Getters
  bool get isRunning => _isRunning;
  double get walletBalance => _walletBalance;
  List<Position> get currentPositions => _currentPositions;
  Signal? get lastSignal => _lastSignal;

  // 봇 초기화
  Future<void> initialize(User user, String apiKey, String apiSecret) async {
    _currentUser = user;
    _settings = await _db.getSettings();

    _api = BybitApiService(
      apiKey: apiKey,
      apiSecret: apiSecret,
      isTestnet: false,
    );

    // Named Pipe 리스너 연결
    await _pipeListener.connect();

    // 신호 구독
    _pipeListener.signalStream.listen(_handleSignal);
  }

  // 봇 시작
  Future<void> start() async {
    if (_api == null || _settings == null) {
      throw Exception('봇이 초기화되지 않았습니다');
    }

    _isRunning = true;

    // 레버리지 설정
    await _api!.setLeverage(
      symbol: _settings!.symbol,
      buyLeverage: _settings!.leverage.toString(),
      sellLeverage: _settings!.leverage.toString(),
    );

    // 정기 업데이트 시작
    _startPeriodicUpdates();

    print('자동매매 봇이 시작되었습니다');
  }

  // 봇 중지
  Future<void> stop() async {
    _isRunning = false;

    _positionCheckTimer?.cancel();
    _balanceUpdateTimer?.cancel();

    print('자동매매 봇이 중지되었습니다');
  }

  // 정기 업데이트 시작
  void _startPeriodicUpdates() {
    // 잔고 업데이트 (30초마다)
    _balanceUpdateTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _updateBalance(),
    );

    // 포지션 업데이트 (10초마다)
    _positionCheckTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) => _updatePositions(),
    );
  }

  // 잔고 업데이트
  Future<void> _updateBalance() async {
    if (_api == null || !_isRunning) return;

    try {
      _walletBalance = await _api!.getWalletBalance();
    } catch (e) {
      print('잔고 업데이트 오류: $e');
    }
  }

  // 포지션 업데이트
  Future<void> _updatePositions() async {
    if (_api == null || !_isRunning) return;

    try {
      _currentPositions = await _api!.getPositions(symbol: _settings!.symbol);
    } catch (e) {
      print('포지션 업데이트 오류: $e');
    }
  }

  // 신호 처리
  void _handleSignal(Signal signal) async {
    if (!_isRunning || _api == null || _settings == null) return;

    _lastSignal = signal;

    try {
      // 기존 포지션 확인
      final positions = await _api!.getPositions(symbol: _settings!.symbol);

      // 반대 포지션이 있으면 먼저 청산
      for (final position in positions) {
        if ((signal.type == 'long' && position.side == 'Sell') ||
            (signal.type == 'short' && position.side == 'Buy')) {
          await _closePosition(position);
        }
      }

      // 새 포지션 오픈
      await _openPosition(signal);

      // 신호 저장
      await _db.saveSignal(signal);
    } catch (e) {
      print('신호 처리 오류: $e');
    }
  }

  // 포지션 오픈
  Future<void> _openPosition(Signal signal) async {
    if (_api == null || _settings == null) return;

    try {
      // 포지션 크기 계산
      final positionSize = _calculatePositionSize();

      final success = await _api!.createOrder(
        symbol: _settings!.symbol,
        side: signal.type == 'long' ? 'Buy' : 'Sell',
        orderType: 'Market',
        qty: positionSize.toString(),
      );

      if (success) {
        print('포지션 오픈 성공: ${signal.type} $positionSize');

        // 거래 기록 저장
        await _db.saveTrade({
          'action': 'open',
          'signal': signal.toJson(),
          'size': positionSize,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      print('포지션 오픈 오류: $e');
    }
  }

  // 포지션 청산
  Future<void> _closePosition(Position position) async {
    if (_api == null || _settings == null) return;

    try {
      final success = await _api!.createOrder(
        symbol: position.symbol,
        side: position.side == 'Buy' ? 'Sell' : 'Buy',
        orderType: 'Market',
        qty: position.size.toString(),
      );

      if (success) {
        print('포지션 청산 성공: ${position.side} ${position.size}');

        // 거래 기록 저장
        await _db.saveTrade({
          'action': 'close',
          'position': position.toJson(),
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      print('포지션 청산 오류: $e');
    }
  }

  // 포지션 크기 계산
  double _calculatePositionSize() {
    if (_settings == null) return 0.0;

    // 투자 비율에 따른 포지션 크기 계산
    final investmentAmount =
        _walletBalance * (_settings!.investmentRatio / 100);

    // 레버리지 적용
    return investmentAmount * _settings!.leverage;
  }

  void dispose() {
    stop();
    _pipeListener.dispose();
    _api?.dispose();
  }
}
