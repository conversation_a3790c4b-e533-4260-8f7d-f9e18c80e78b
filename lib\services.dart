// lib/services.dart
export 'services/database.dart';
export 'services/crypto.dart';
export 'services/pipe_listener.dart';
export 'services/bybit_api.dart';
export 'services/trading_bot.dart';

// TradingBot
class TradingBot {
  final DatabaseService _db = DatabaseService();
  final PipeListener _pipeListener = PipeListener();

  BybitApiService? _api;
  Timer? _positionCheckTimer;
  Timer? _balanceUpdateTimer;
  bool _isRunning = false;
  Settings? _settings;

  // 상태
  double _walletBalance = 0.0;
  List<Position> _currentPositions = [];
  Signal? _lastSignal;

  // Getters
  bool get isRunning => _isRunning;
  double get walletBalance => _walletBalance;
  List<Position> get currentPositions => _currentPositions;
  Signal? get lastSignal => _lastSignal;

  // 봇 초기화
  Future<void> initialize(User user, String apiKey, String apiSecret) async {
    _currentUser = user;
    _settings = await _db.getSettings();

    _api = BybitApiService(
      apiKey: apiKey,
      apiSecret: apiSecret,
      isTestnet: false,
    );

    // Named Pipe 리스너 연결
    await _pipeListener.connect();

    // 신호 구독
    _pipeListener.signalStream.listen(_handleSignal);
  }

  // 봇 시작
  Future<void> start() async {
    if (_api == null || _settings == null) {
      throw Exception('봇이 초기화되지 않았습니다');
    }

    _isRunning = true;

    // 레버리지 설정
    await _api!.setLeverage(
      symbol: _settings!.symbol,
      buyLeverage: _settings!.leverage.toString(),
      sellLeverage: _settings!.leverage.toString(),
    );

    // 정기 업데이트 시작
    _startPeriodicUpdates();

    print('자동매매 봇이 시작되었습니다');
  }

  // 봇 중지
  Future<void> stop() async {
    _isRunning = false;

    _positionCheckTimer?.cancel();
    _balanceUpdateTimer?.cancel();

    print('자동매매 봇이 중지되었습니다');
  }

  // 정기 업데이트 시작
  void _startPeriodicUpdates() {
    // 잔고 업데이트 (30초마다)
    _balanceUpdateTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _updateBalance(),
    );

    // 포지션 업데이트 (10초마다)
    _positionCheckTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) => _updatePositions(),
    );
  }

  // 잔고 업데이트
  Future<void> _updateBalance() async {
    if (_api == null || !_isRunning) return;

    try {
      _walletBalance = await _api!.getWalletBalance();
    } catch (e) {
      print('잔고 업데이트 오류: $e');
    }
  }

  // 포지션 업데이트
  Future<void> _updatePositions() async {
    if (_api == null || !_isRunning) return;

    try {
      _currentPositions = await _api!.getPositions(symbol: _settings!.symbol);
    } catch (e) {
      print('포지션 업데이트 오류: $e');
    }
  }

  // 신호 처리
  void _handleSignal(Signal signal) async {
    if (!_isRunning || _api == null || _settings == null) return;

    _lastSignal = signal;

    try {
      // 기존 포지션 확인
      final positions = await _api!.getPositions(symbol: _settings!.symbol);

      // 반대 포지션이 있으면 먼저 청산
      for (final position in positions) {
        if ((signal.type == 'long' && position.side == 'Sell') ||
            (signal.type == 'short' && position.side == 'Buy')) {
          await _closePosition(position);
        }
      }

      // 새 포지션 오픈
      await _openPosition(signal);

      // 신호 저장
      await _db.saveSignal(signal);
    } catch (e) {
      print('신호 처리 오류: $e');
    }
  }

  // 포지션 오픈
  Future<void> _openPosition(Signal signal) async {
    if (_api == null || _settings == null) return;

    try {
      // 포지션 크기 계산
      final positionSize = _calculatePositionSize();

      final success = await _api!.createOrder(
        symbol: _settings!.symbol,
        side: signal.type == 'long' ? 'Buy' : 'Sell',
        orderType: 'Market',
        qty: positionSize.toString(),
      );

      if (success) {
        print('포지션 오픈 성공: ${signal.type} $positionSize');

        // 거래 기록 저장
        await _db.saveTrade({
          'action': 'open',
          'signal': signal.toJson(),
          'size': positionSize,
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      print('포지션 오픈 오류: $e');
    }
  }

  // 포지션 청산
  Future<void> _closePosition(Position position) async {
    if (_api == null || _settings == null) return;

    try {
      final success = await _api!.createOrder(
        symbol: position.symbol,
        side: position.side == 'Buy' ? 'Sell' : 'Buy',
        orderType: 'Market',
        qty: position.size.toString(),
      );

      if (success) {
        print('포지션 청산 성공: ${position.side} ${position.size}');

        // 거래 기록 저장
        await _db.saveTrade({
          'action': 'close',
          'position': position.toJson(),
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      print('포지션 청산 오류: $e');
    }
  }

  // 포지션 크기 계산
  double _calculatePositionSize() {
    if (_settings == null) return 0.0;

    // 투자 비율에 따른 포지션 크기 계산
    final investmentAmount =
        _walletBalance * (_settings!.investmentRatio / 100);

    // 레버리지 적용
    return investmentAmount * _settings!.leverage;
  }

  void dispose() {
    stop();
    _pipeListener.dispose();
    _api?.dispose();
  }
}
