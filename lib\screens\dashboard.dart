// lib/screens/dashboard.dart
import 'package:flutter/material.dart';
import 'dart:async';
import '../models.dart';
import '../services.dart';
import '../constants.dart';
import '../widgets.dart';
import 'settings.dart';
import 'login.dart';

class DashboardScreen extends StatefulWidget {
  final User user;

  const DashboardScreen({
    super.key,
    required this.user,
  });

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseService _db = DatabaseService();
  final TradingBot _bot = TradingBot();

  // 상태
  bool _isApiConnected = false;
  Timer? _updateTimer;

  // 로그
  final List<LogEntry> _logs = [];
  final _logScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    await _db.init();

    // API 키 확인
    if (widget.user.encryptedApiKey.isEmpty ||
        widget.user.encryptedApiSecret.isEmpty) {
      _showApiKeyDialog();
    } else {
      await _connectBot();
    }

    // 업데이트 타이머 시작
    _updateTimer = Timer.periodic(
      const Duration(seconds: 1),
      (_) => setState(() {}),
    );
  }

  Future<void> _connectBot() async {
    try {
      final apiKeys = CryptoService.decryptApiKeys(
        encryptedApiKey: widget.user.encryptedApiKey,
        encryptedApiSecret: widget.user.encryptedApiSecret,
      );

      await _bot.initialize(
        widget.user,
        apiKeys['apiKey']!,
        apiKeys['apiSecret']!,
      );

      setState(() => _isApiConnected = true);
      _addLog('API 연결 성공', LogLevel.success);
    } catch (e) {
      _addLog('API 연결 실패: $e', LogLevel.error);
    }
  }

  void _toggleBot() async {
    if (_bot.isRunning) {
      await _bot.stop();
      _addLog('봇 중지됨', LogLevel.warning);
    } else {
      await _bot.start();
      _addLog('봇 시작됨', LogLevel.success);
    }
    setState(() {});
  }

  void _addLog(String message, LogLevel level) {
    setState(() {
      _logs.insert(
          0,
          LogEntry(
            message: message,
            timestamp: DateTime.now(),
            level: level,
          ));

      if (_logs.length > 50) {
        _logs.removeRange(50, _logs.length);
      }
    });
  }

  void _showApiKeyDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ApiKeyDialog(
        user: widget.user,
        onSaved: () {
          Navigator.pop(context);
          _connectBot();
        },
      ),
    );
  }

  Future<void> _logout() async {
    await _db.clearCurrentUser();
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (_) => const LoginScreen()),
      );
    }
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _logScrollController.dispose();
    _bot.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // 헤더
            _buildHeader(),

            // 메인 컨텐츠
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 봇 상태
                    BotStatusCard(
                      isRunning: _bot.isRunning,
                      isConnected: _isApiConnected,
                    ),
                    const SizedBox(height: 16),

                    // 자산 현황
                    AssetCard(
                      balance: _bot.walletBalance,
                      isConnected: _isApiConnected,
                    ),
                    const SizedBox(height: 16),

                    // 거래 설정
                    TradingSettingsCard(
                      user: widget.user,
                      onStartBot: _toggleBot,
                      isRunning: _bot.isRunning,
                    ),
                    const SizedBox(height: 16),

                    // 포지션 신호정보
                    SignalInfoCard(
                      lastSignal: _bot.lastSignal,
                      positions: _bot.currentPositions,
                    ),
                    const SizedBox(height: 16),

                    // 회원 랭킹 리스트
                    const RankingCard(),
                    const SizedBox(height: 16),

                    // 로그
                    LogCard(
                      logs: _logs,
                      scrollController: _logScrollController,
                      onClear: () => setState(() => _logs.clear()),
                    ),
                    const SizedBox(height: 16),

                    // 신호 출력 표시
                    if (_bot.lastSignal != null)
                      SignalDisplayCard(signal: _bot.lastSignal!),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05 * 255),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.1 * 255),
          ),
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 로고
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppConstants.primaryColor.withValues(alpha: 0.08 * 255),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppConstants.primaryColor.withValues(alpha: 0.3 * 255),
              ),
            ),
            child: const Icon(
              Icons.currency_bitcoin,
              size: 24,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(width: 12),

          // 타이틀
          const Text(
            'Open Systems_Bot',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          const Spacer(),

          // 액션 버튼
          IconButton(
            icon: const Icon(Icons.key, color: Colors.grey),
            onPressed: _showApiKeyDialog,
            tooltip: 'API 설정',
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: AppConstants.primaryColor),
            onPressed: widget.user.isAdmin
                ? () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => SettingsScreen(user: widget.user),
                      ),
                    );
                  }
                : null,
            tooltip: '봇 설정',
          ),
          IconButton(
            icon: const Icon(Icons.logout, color: Colors.grey),
            onPressed: _logout,
            tooltip: '로그아웃',
          ),
        ],
      ),
    );
  }
}

// API 키 설정 다이얼로그
class ApiKeyDialog extends StatefulWidget {
  final User user;
  final VoidCallback onSaved;

  const ApiKeyDialog({
    super.key,
    required this.user,
    required this.onSaved,
  });

  @override
  State<ApiKeyDialog> createState() => _ApiKeyDialogState();
}

class _ApiKeyDialogState extends State<ApiKeyDialog> {
  final _apiKeyController = TextEditingController();
  final _apiSecretController = TextEditingController();
  final DatabaseService _db = DatabaseService();

  bool _obscureKey = true;
  bool _obscureSecret = true;

  @override
  void initState() {
    super.initState();
    _db.init();
  }

  Future<void> _save() async {
    if (_apiKeyController.text.isEmpty || _apiSecretController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('API Key와 Secret을 모두 입력해주세요')),
      );
      return;
    }

    // API 키 암호화 및 저장
    final encrypted = CryptoService.encryptApiKeys(
      apiKey: _apiKeyController.text,
      apiSecret: _apiSecretController.text,
    );

    final updatedUser = widget.user.copyWith(
      encryptedApiKey: encrypted['apiKey'],
      encryptedApiSecret: encrypted['apiSecret'],
    );

    await _db.saveUser(updatedUser);

    widget.onSaved();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppConstants.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 아이콘
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppConstants.primaryColor.withValues(alpha: 0.08 * 255),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: AppConstants.primaryColor.withValues(alpha: 0.3 * 255),
                ),
              ),
              child: const Icon(
                Icons.key,
                size: 30,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 16),

            const Text(
              'API 설정',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '바이비트 API 정보를 입력하세요',
              style: TextStyle(
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),

            // API Key 입력
            TextField(
              controller: _apiKeyController,
              obscureText: _obscureKey,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                labelText: 'API Key',
                labelStyle: TextStyle(color: Colors.grey[400]),
                prefixIcon:
                    const Icon(Icons.key, color: AppConstants.primaryColor),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureKey ? Icons.visibility : Icons.visibility_off,
                    color: Colors.grey,
                  ),
                  onPressed: () => setState(() => _obscureKey = !_obscureKey),
                ),
                filled: true,
                fillColor: Colors.white.withValues(alpha: 0.05 * 255),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // API Secret 입력
            TextField(
              controller: _apiSecretController,
              obscureText: _obscureSecret,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                labelText: 'API Secret',
                labelStyle: TextStyle(color: Colors.grey[400]),
                prefixIcon:
                    const Icon(Icons.lock, color: AppConstants.primaryColor),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureSecret ? Icons.visibility : Icons.visibility_off,
                    color: Colors.grey,
                  ),
                  onPressed: () =>
                      setState(() => _obscureSecret = !_obscureSecret),
                ),
                filled: true,
                fillColor: Colors.white.withValues(alpha: 0.05),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 버튼
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '취소',
                      style: TextStyle(color: Colors.grey[400]),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _save,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor
                          .withValues(alpha: 0.08 * 255),
                      foregroundColor: AppConstants.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: AppConstants.primaryColor
                              .withValues(alpha: 0.3 * 255),
                        ),
                      ),
                    ),
                    child: const Text(
                      '저장',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _apiSecretController.dispose();
    super.dispose();
  }
}
