import 'dart:io';
import 'dart:convert';

void main() async {
  final server = await HttpServer.bind('0.0.0.0', 8080);
  print('🚀 서버가 http://0.0.0.0:8080 에서 실행 중입니다');
  print('🌐 외부 접속: http://58.236.168.67:8080');

  await for (HttpRequest request in server) {
    handleRequest(request);
  }
}

Future<void> handleRequest(HttpRequest request) async {
  final uri = request.uri;
  var path = uri.path;

  // 기본 경로 처리
  if (path == '/') {
    path = '/index.html';
  }

  // 파일 경로 결정
  final filePath = 'build/web${path}';
  final file = File(filePath);

  try {
    if (await file.exists()) {
      // MIME 타입 결정
      String contentType = 'text/html';
      if (path.endsWith('.js')) {
        contentType = 'application/javascript';
      } else if (path.endsWith('.css')) {
        contentType = 'text/css';
      } else if (path.endsWith('.json')) {
        contentType = 'application/json';
      } else if (path.endsWith('.wasm')) {
        contentType = 'application/wasm';
      } else if (path.endsWith('.png')) {
        contentType = 'image/png';
      } else if (path.endsWith('.ico')) {
        contentType = 'image/x-icon';
      }

      // CORS 헤더 추가
      request.response.headers.set('Access-Control-Allow-Origin', '*');
      request.response.headers
          .set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      request.response.headers
          .set('Access-Control-Allow-Headers', 'Content-Type');
      request.response.headers.contentType = ContentType.parse(contentType);

      // 파일 내용 전송
      await file.openRead().pipe(request.response);
    } else {
      // 404 에러
      request.response.statusCode = HttpStatus.notFound;
      request.response.write('File not found: $path');
      await request.response.close();
    }
  } catch (e) {
    // 서버 에러
    request.response.statusCode = HttpStatus.internalServerError;
    request.response.write('Server error: $e');
    await request.response.close();
  }
}
