# 📋 Open Systems_Bot - 바이비트 자동매매 시스템

## 🚀 시스템 개요

Open Systems_Bot은 바이비트(Bybit) 무기한 선물 거래를 위한 전문 자동매매 봇입니다. 외부 신호 감지 애플리케이션과 연동하여 실시간으로 매매 신호를 수신하고, 양방향 포지션 전략을 실행합니다.

## ✨ 핵심 특징

- **실거래 전용**: 테스트넷 미지원, 실제 계정에서만 작동
- **외부 신호 의존**: 지표 사용 없이 외부 앱의 신호만 활용
- **양방향 포지션**: 롱/숏 동시 진입 전략
- **Named Pipe 통신**: Windows 전용 실시간 통신
- **권한 분리**: 관리자/일반 사용자 구분
- **보안 강화**: API 키와 비밀번호 암호화 저장

## 🏗️ 시스템 아키텍처

```
외부 신호 감지 앱(.exe)
    ↓ [Named Pipe: \\.\pipe\RealtimeCaptureLogPipe]
Named Pipe Server (port 8080)
    ↓ [WebSocket: ws://localhost:8080/pipe]
Flutter Bot (Named Pipe Client)
    ↓ [신호 파싱 및 처리]
매매 실행 엔진 (Bybit API)
```

## 📦 설치 및 실행

### 1. 시스템 요구사항
- **OS**: Windows 10/11 (관리자 권한 필요)
- **Flutter SDK**: 3.0.0 이상
- **Dart SDK**: 3.0.0 이상

### 2. 프로젝트 설치
```bash
git clone <repository-url>
cd OPENSYSTEMS_BOT
flutter pub get
```

### 3. Named Pipe 서버 시작
```bash
# 관리자 권한으로 실행
start_server.bat

# 또는 직접 실행
dart run bin/server.dart
```

### 4. Flutter 앱 실행
```bash
flutter run -d chrome
```

## 🔧 설정 방법

### 1. 관리자 계정
- **이메일**: <EMAIL>
- **비밀번호**: admin123

### 2. API 키 설정
1. 바이비트에서 API 키 생성
2. 앱에서 API 설정 메뉴 접근
3. API Key와 Secret 입력
4. 자동 암호화 저장

### 3. 거래 설정
- **거래 페어**: BTCUSDT, ETHUSDT 등
- **레버리지**: 1X ~ 20X
- **투자 비율**: 25%, 50%, 75%, 100%
- **손절/익절**: ±0.5% (기본값)

## 📊 주요 기능

### 신호 처리
- **유효 패턴**: "롱 신호가 감지되었습니다", "숏 신호가 감지되었습니다"
- **무시 패턴**: DEBUG 메시지, Frame logging 등
- **중복 방지**: 동일 신호 ID 필터링

### 양방향 포지션 전략
1. **투자 금액 계산**: 전체 잔고 × 투자비율 ÷ 2
2. **포지션 진입**: 지정가 주문 (슬리피지 0.05%)
3. **손절**: -0.5% 도달시 시장가 즉시 청산
4. **익절**: +0.5% 도달시 신호 대기
5. **모니터링**: 0.5초 간격 실시간 체크

### 보안 시스템
- **암호화**: API Key/Secret AES-256 암호화
- **해싱**: 사용자 비밀번호 SHA-256 해싱
- **권한 관리**: 관리자/일반 사용자 구분
- **세션 관리**: 자동 로그인 유지

## 🎯 사용 방법

### 1. 시스템 시작
1. Named Pipe 서버 실행 (관리자 권한)
2. 외부 신호 감지 앱 실행
3. Flutter 봇 실행
4. API 키 설정 완료

### 2. 봇 운영
1. 거래 설정 확인
2. 봇 시작 버튼 클릭
3. 실시간 로그 모니터링
4. 포지션 상태 확인

### 3. 모니터링
- **봇 상태**: 실행/중지 상태 표시
- **자산 현황**: 총 자산, 수익률
- **신호 정보**: 최근 신호, 포지션 상태
- **실시간 로그**: 시스템 로그, 거래 로그

## 🔍 문제 해결

### 연결 문제
- **Named Pipe 연결 실패**: 외부 앱 먼저 실행
- **API 연결 실패**: 네트워크 및 API 키 확인

### 거래 문제
- **주문 실패**: 잔고 부족 또는 레버리지 설정 확인
- **신호 미수신**: 로그 패턴 및 외부 앱 상태 확인

### 권한 문제
- **관리자 권한**: Windows에서 관리자 권한으로 실행
- **Named Pipe 접근**: 관리자 권한 필요

## ⚠️ 주의사항

1. **실거래 전용**: 실제 자금으로만 거래
2. **24시간 모니터링**: 봇 실행 중 PC 켜두기
3. **정기적 점검**: 포지션 및 잔고 확인
4. **백업**: 설정 및 거래 기록 정기 백업
5. **리스크 관리**: 투자 비율 적절히 설정

## 📈 통계 및 모니터링

### 서버 통계
- **엔드포인트**: http://localhost:8080/stats
- **정보**: 가동시간, 메시지 수신 횟수, 신호 감지 횟수

### 실시간 로그
- **레벨**: INFO, WARNING, ERROR, SUCCESS, SIGNAL, TRADE
- **필터링**: 로그 레벨별 색상 구분
- **저장**: 최근 500개 로그 유지

## 🛠️ 개발 정보

### 기술 스택
- **Frontend**: Flutter (Web)
- **Backend**: Dart (Named Pipe Server)
- **Database**: SharedPreferences (암호화)
- **API**: Bybit REST API
- **통신**: Named Pipe + WebSocket

### 프로젝트 구조
```
lib/
├── models/          # 데이터 모델
├── services/        # 핵심 서비스
├── screens/         # UI 화면
├── widgets/         # 재사용 위젯
├── utils/           # 유틸리티
├── server/          # Named Pipe 서버
└── constants.dart   # 상수 정의
```

## 📞 지원

문제가 발생하거나 문의사항이 있으시면 개발팀에 연락해주세요.

---

**⚡ Open Systems_Bot - 전문 자동매매의 새로운 기준**
