// lib/services/crypto.dart
import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

class CryptoService {
  // AES-256 암호화를 위한 키 (실제 구현에서는 더 안전한 키 관리 필요)
  static const String _encryptionKey = 'OpenSystemsBot2024SecureKey12345';

  // XOR 암호화 (웹 호환성을 위해 간단한 구현)
  static String _xorEncode(String input, String key) {
    final inputBytes = utf8.encode(input);
    final keyBytes = utf8.encode(key);
    final result = <int>[];

    for (int i = 0; i < inputBytes.length; i++) {
      result.add(inputBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64.encode(result);
  }

  static String _xorDecode(String encoded, String key) {
    try {
      final encodedBytes = base64.decode(encoded);
      final keyBytes = utf8.encode(key);
      final result = <int>[];

      for (int i = 0; i < encodedBytes.length; i++) {
        result.add(encodedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return utf8.decode(result);
    } catch (e) {
      return '';
    }
  }

  // 문자열 암호화
  static String encryptString(String plainText) {
    if (plainText.isEmpty) return '';
    return _xorEncode(plainText, _encryptionKey);
  }

  // 문자열 복호화
  static String decryptString(String encryptedText) {
    if (encryptedText.isEmpty) return '';
    return _xorDecode(encryptedText, _encryptionKey);
  }

  // 비밀번호 해싱 (SHA-256 기반)
  static String hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 비밀번호 검증
  static bool verifyPassword(String password, String hash) {
    return hashPassword(password) == hash;
  }

  // API 키 암호화
  static Map<String, String> encryptApiKeys({
    required String apiKey,
    required String apiSecret,
  }) {
    return {
      'apiKey': encryptString(apiKey),
      'apiSecret': encryptString(apiSecret),
    };
  }

  // API 키 복호화
  static Map<String, String> decryptApiKeys({
    required String encryptedApiKey,
    required String encryptedApiSecret,
  }) {
    if (encryptedApiKey.isEmpty || encryptedApiSecret.isEmpty) {
      return {'apiKey': '', 'apiSecret': ''};
    }

    return {
      'apiKey': decryptString(encryptedApiKey),
      'apiSecret': decryptString(encryptedApiSecret),
    };
  }

  // 안전한 랜덤 문자열 생성
  static String generateRandomString(int length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    final result = StringBuffer();
    
    for (int i = 0; i < length; i++) {
      result.write(chars[(random + i) % chars.length]);
    }
    
    return result.toString();
  }

  // 데이터 무결성 검증을 위한 해시 생성
  static String generateDataHash(Map<String, dynamic> data) {
    final jsonString = jsonEncode(data);
    final bytes = utf8.encode(jsonString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 데이터 무결성 검증
  static bool verifyDataIntegrity(Map<String, dynamic> data, String expectedHash) {
    final actualHash = generateDataHash(data);
    return actualHash == expectedHash;
  }

  // 민감한 데이터 마스킹
  static String maskSensitiveData(String data, {int visibleChars = 4}) {
    if (data.length <= visibleChars * 2) {
      return '*' * data.length;
    }
    
    final start = data.substring(0, visibleChars);
    final end = data.substring(data.length - visibleChars);
    final middle = '*' * (data.length - visibleChars * 2);
    
    return '$start$middle$end';
  }

  // API 키 유효성 검증
  static bool isValidApiKey(String apiKey) {
    // 바이비트 API 키 형식 검증 (일반적으로 32자 이상)
    if (apiKey.isEmpty || apiKey.length < 20) return false;
    
    // 알파벳과 숫자만 포함하는지 확인
    final regex = RegExp(r'^[a-zA-Z0-9]+$');
    return regex.hasMatch(apiKey);
  }

  // API 시크릿 유효성 검증
  static bool isValidApiSecret(String apiSecret) {
    // 바이비트 API 시크릿 형식 검증
    if (apiSecret.isEmpty || apiSecret.length < 20) return false;
    
    // 알파벳과 숫자만 포함하는지 확인
    final regex = RegExp(r'^[a-zA-Z0-9]+$');
    return regex.hasMatch(apiSecret);
  }

  // 암호화된 데이터의 유효성 검증
  static bool isValidEncryptedData(String encryptedData) {
    if (encryptedData.isEmpty) return false;
    
    try {
      base64.decode(encryptedData);
      return true;
    } catch (e) {
      return false;
    }
  }

  // 보안 설정 검증
  static Map<String, bool> validateSecuritySettings({
    required String apiKey,
    required String apiSecret,
    String? encryptedApiKey,
    String? encryptedApiSecret,
  }) {
    return {
      'validApiKey': isValidApiKey(apiKey),
      'validApiSecret': isValidApiSecret(apiSecret),
      'validEncryptedApiKey': encryptedApiKey != null ? isValidEncryptedData(encryptedApiKey) : true,
      'validEncryptedApiSecret': encryptedApiSecret != null ? isValidEncryptedData(encryptedApiSecret) : true,
    };
  }

  // 암호화 키 강도 검증
  static bool isStrongPassword(String password) {
    if (password.length < 8) return false;
    
    // 최소 하나의 대문자, 소문자, 숫자, 특수문자 포함
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  // 보안 점수 계산
  static int calculateSecurityScore({
    required String password,
    required String apiKey,
    required String apiSecret,
  }) {
    int score = 0;
    
    // 비밀번호 강도 (40점)
    if (isStrongPassword(password)) score += 40;
    else if (password.length >= 8) score += 20;
    else if (password.length >= 6) score += 10;
    
    // API 키 유효성 (30점)
    if (isValidApiKey(apiKey)) score += 30;
    else if (apiKey.isNotEmpty) score += 10;
    
    // API 시크릿 유효성 (30점)
    if (isValidApiSecret(apiSecret)) score += 30;
    else if (apiSecret.isNotEmpty) score += 10;
    
    return score;
  }

  // 보안 권장사항 생성
  static List<String> getSecurityRecommendations({
    required String password,
    required String apiKey,
    required String apiSecret,
  }) {
    final recommendations = <String>[];
    
    if (!isStrongPassword(password)) {
      recommendations.add('비밀번호는 8자 이상이며 대소문자, 숫자, 특수문자를 포함해야 합니다.');
    }
    
    if (!isValidApiKey(apiKey)) {
      recommendations.add('유효한 바이비트 API 키를 입력해주세요.');
    }
    
    if (!isValidApiSecret(apiSecret)) {
      recommendations.add('유효한 바이비트 API 시크릿을 입력해주세요.');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('보안 설정이 양호합니다.');
    }
    
    return recommendations;
  }
}
