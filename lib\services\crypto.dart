// lib/services/crypto.dart
import 'dart:convert';
import 'package:crypto/crypto.dart';

class CryptoService {
  // 간단한 XOR 암호화 키 (실제로는 더 안전한 방식으로 관리)
  static const String _encryptionKey = 'OpenSystemsBot2024SecureKey12345';

  // 간단한 XOR 암호화 (웹 호환)
  static String encryptAES(String plainText) {
    final keyBytes = utf8.encode(_encryptionKey);
    final textBytes = utf8.encode(plainText);
    final encryptedBytes = <int>[];

    for (int i = 0; i < textBytes.length; i++) {
      encryptedBytes.add(textBytes[i] ^ keyBytes[i % keyBytes.length]);
    }

    return base64Encode(encryptedBytes);
  }

  // 간단한 XOR 복호화 (웹 호환)
  static String decryptAES(String encryptedText) {
    try {
      final keyBytes = utf8.encode(_encryptionKey);
      final encryptedBytes = base64Decode(encryptedText);
      final decryptedBytes = <int>[];

      for (int i = 0; i < encryptedBytes.length; i++) {
        decryptedBytes.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return utf8.decode(decryptedBytes);
    } catch (e) {
      return '';
    }
  }

  // 비밀번호 해싱 (간단한 구현)
  static String hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 비밀번호 검증
  static bool verifyPassword(String password, String hash) {
    return hashPassword(password) == hash;
  }

  // API 키 암호화
  static Map<String, String> encryptApiKeys({
    required String apiKey,
    required String apiSecret,
  }) {
    return {
      'apiKey': encryptAES(apiKey),
      'apiSecret': encryptAES(apiSecret),
    };
  }

  // API 키 복호화
  static Map<String, String> decryptApiKeys({
    required String encryptedApiKey,
    required String encryptedApiSecret,
  }) {
    if (encryptedApiKey.isEmpty || encryptedApiSecret.isEmpty) {
      return {'apiKey': '', 'apiSecret': ''};
    }

    return {
      'apiKey': decryptAES(encryptedApiKey),
      'apiSecret': decryptAES(encryptedApiSecret),
    };
  }
}
