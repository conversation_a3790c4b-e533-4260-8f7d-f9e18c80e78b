// lib/services/database.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models.dart';

class DatabaseService {
  late SharedPreferences _prefs;

  // JSON 키
  static const String _usersKey = 'users';
  static const String _settingsKey = 'settings';
  static const String _tradesKey = 'trades';
  static const String _signalsKey = 'signals';
  static const String _logsKey = 'logs';
  static const String _currentUserKey = 'currentUser';

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _initializeDefaults();
  }

  Future<void> _initializeDefaults() async {
    // 관리자 계정 초기화
    if (!_prefs.containsKey(_usersKey)) {
      final adminUser = User(
        email: '<EMAIL>',
        name: '관리자',
        passwordHash: _hashPassword('admin123'),
        isAdmin: true,
        createdAt: DateTime.now(),
      );

      await saveUser(adminUser);
    }

    // 기본 설정 초기화
    if (!_prefs.containsKey(_settingsKey)) {
      await saveSettings(Settings());
    }
  }

  // 비밀번호 해싱 (간단한 구현, 실제로는 bcrypt 사용)
  String _hashPassword(String password) {
    // TODO: 실제 구현시 bcrypt 사용
    return base64.encode(utf8.encode(password));
  }

  // 사용자 관련
  Future<void> saveUser(User user) async {
    final users = await getAllUsers();
    users[user.email] = user;

    final jsonString =
        jsonEncode(users.map((key, value) => MapEntry(key, value.toJson())));
    await _prefs.setString(_usersKey, jsonString);
  }

  Future<Map<String, User>> getAllUsers() async {
    final jsonString = _prefs.getString(_usersKey);
    if (jsonString == null) return {};

    final Map<String, dynamic> json = jsonDecode(jsonString);
    return json.map((key, value) => MapEntry(key, User.fromJson(value)));
  }

  Future<User?> getUser(String email) async {
    final users = await getAllUsers();
    return users[email];
  }

  Future<User?> getCurrentUser() async {
    final email = _prefs.getString(_currentUserKey);
    if (email == null) return null;
    return getUser(email);
  }

  Future<void> setCurrentUser(String email) async {
    await _prefs.setString(_currentUserKey, email);
  }

  Future<void> clearCurrentUser() async {
    await _prefs.remove(_currentUserKey);
  }

  // 설정 관련
  Future<void> saveSettings(Settings settings) async {
    await _prefs.setString(_settingsKey, jsonEncode(settings.toJson()));
  }

  Future<Settings> getSettings() async {
    final jsonString = _prefs.getString(_settingsKey);
    if (jsonString == null) return Settings();
    return Settings.fromJson(jsonDecode(jsonString));
  }

  // 신호 관련
  Future<void> saveSignal(Signal signal) async {
    final signals = await getAllSignals();
    signals.add(signal);

    // 최근 100개만 유지
    if (signals.length > 100) {
      signals.removeRange(0, signals.length - 100);
    }

    final jsonString = jsonEncode(signals.map((s) => s.toJson()).toList());
    await _prefs.setString(_signalsKey, jsonString);
  }

  Future<List<Signal>> getAllSignals() async {
    final jsonString = _prefs.getString(_signalsKey);
    if (jsonString == null) return [];

    final List<dynamic> json = jsonDecode(jsonString);
    return json.map((item) => Signal.fromJson(item)).toList();
  }

  // 거래 기록
  Future<void> saveTrade(Map<String, dynamic> trade) async {
    final trades = await getAllTrades();
    trades.add(trade);

    // 최근 1000개만 유지
    if (trades.length > 1000) {
      trades.removeRange(0, trades.length - 1000);
    }

    await _prefs.setString(_tradesKey, jsonEncode(trades));
  }

  Future<List<Map<String, dynamic>>> getAllTrades() async {
    final jsonString = _prefs.getString(_tradesKey);
    if (jsonString == null) return [];

    final List<dynamic> json = jsonDecode(jsonString);
    return json.cast<Map<String, dynamic>>();
  }

  // 로그
  Future<void> saveLog(LogEntry logEntry) async {
    final logs = await getAllLogs();
    logs.add(logEntry);

    // 최근 500개만 유지
    if (logs.length > 500) {
      logs.removeRange(0, logs.length - 500);
    }

    final jsonString = jsonEncode(logs.map((log) => log.toJson()).toList());
    await _prefs.setString(_logsKey, jsonString);
  }

  Future<List<LogEntry>> getAllLogs() async {
    final jsonString = _prefs.getString(_logsKey);
    if (jsonString == null) return [];

    final List<dynamic> json = jsonDecode(jsonString);
    return json.map((item) => LogEntry.fromJson(item)).toList();
  }

  // 로그 추가 헬퍼 메서드
  Future<void> addLog(String message, LogLevel level,
      [Map<String, dynamic>? metadata]) async {
    final logEntry = LogEntry(
      message: message,
      timestamp: DateTime.now(),
      level: level,
      metadata: metadata,
    );
    await saveLog(logEntry);
  }

  // 전체 데이터 초기화
  Future<void> clearAllData() async {
    await _prefs.clear();
    await _initializeDefaults();
  }

  // 비밀번호 검증
  bool verifyPassword(String password, String hash) {
    return _hashPassword(password) == hash;
  }

  // 사용자 인증
  Future<User?> authenticateUser(String email, String password) async {
    final user = await getUser(email);
    if (user == null) return null;

    if (verifyPassword(password, user.passwordHash)) {
      await setCurrentUser(email);
      return user;
    }

    return null;
  }

  // 사용자 등록
  Future<bool> registerUser({
    required String email,
    required String name,
    required String password,
    bool isAdmin = false,
  }) async {
    // 이미 존재하는 사용자인지 확인
    final existingUser = await getUser(email);
    if (existingUser != null) return false;

    final user = User(
      email: email,
      name: name,
      passwordHash: _hashPassword(password),
      isAdmin: isAdmin,
      createdAt: DateTime.now(),
    );

    await saveUser(user);
    return true;
  }

  // API 키 업데이트
  Future<void> updateUserApiKeys(
      String email, String encryptedApiKey, String encryptedApiSecret) async {
    final user = await getUser(email);
    if (user == null) return;

    final updatedUser = user.copyWith(
      encryptedApiKey: encryptedApiKey,
      encryptedApiSecret: encryptedApiSecret,
    );

    await saveUser(updatedUser);
  }

  // 개인 설정 업데이트
  Future<void> updateUserPersonalSettings(
      String email, Map<String, dynamic> personalSettings) async {
    final user = await getUser(email);
    if (user == null) return;

    final updatedUser = user.copyWith(
      personalSettings: personalSettings,
    );

    await saveUser(updatedUser);
  }
}
