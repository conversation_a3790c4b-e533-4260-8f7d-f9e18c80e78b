// lib/models.dart

// User 모델
class User {
  final String email;
  final String name;
  final String passwordHash;
  final String encryptedApiKey;
  final String encryptedApiSecret;
  final bool isAdmin;
  final DateTime createdAt;
  final Map<String, dynamic> personalSettings;

  User({
    required this.email,
    required this.name,
    required this.passwordHash,
    this.encryptedApiKey = '',
    this.encryptedApiSecret = '',
    this.isAdmin = false,
    required this.createdAt,
    this.personalSettings = const {},
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      email: json['email'],
      name: json['name'],
      passwordHash: json['passwordHash'],
      encryptedApiKey: json['encryptedApiKey'] ?? '',
      encryptedApiSecret: json['encryptedApiSecret'] ?? '',
      isAdmin: json['isAdmin'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      personalSettings: json['personalSettings'] ?? {},
    );
  }

  Map<String, dynamic> toJson() => {
        'email': email,
        'name': name,
        'passwordHash': passwordHash,
        'encryptedApiKey': encryptedApiKey,
        'encryptedApiSecret': encryptedApiSecret,
        'isAdmin': isAdmin,
        'createdAt': createdAt.toIso8601String(),
        'personalSettings': personalSettings,
      };

  User copyWith({
    String? encryptedApiKey,
    String? encryptedApiSecret,
    Map<String, dynamic>? personalSettings,
  }) {
    return User(
      email: email,
      name: name,
      passwordHash: passwordHash,
      encryptedApiKey: encryptedApiKey ?? this.encryptedApiKey,
      encryptedApiSecret: encryptedApiSecret ?? this.encryptedApiSecret,
      isAdmin: isAdmin,
      createdAt: createdAt,
      personalSettings: personalSettings ?? this.personalSettings,
    );
  }
}

// Signal 모델
class Signal {
  final String id;
  final String type; // 'long' or 'short'
  final DateTime timestamp;
  final String source; // 'manual', 'auto', 'pipe'
  final Map<String, dynamic> metadata;

  Signal({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.source,
    this.metadata = const {},
  });

  factory Signal.fromJson(Map<String, dynamic> json) {
    return Signal(
      id: json['id'],
      type: json['type'],
      timestamp: DateTime.parse(json['timestamp']),
      source: json['source'],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'type': type,
        'timestamp': timestamp.toIso8601String(),
        'source': source,
        'metadata': metadata,
      };
}

// Position 모델
class Position {
  final String id;
  final String symbol;
  final String side; // 'Buy' or 'Sell'
  final double size;
  final double entryPrice;
  final double markPrice;
  final double unrealisedPnl;
  final double unrealisedPnlPercent;
  final String positionIdx; // '1' for Buy, '2' for Sell
  final DateTime createdAt;

  Position({
    required this.id,
    required this.symbol,
    required this.side,
    required this.size,
    required this.entryPrice,
    required this.markPrice,
    required this.unrealisedPnl,
    required this.unrealisedPnlPercent,
    required this.positionIdx,
    required this.createdAt,
  });

  factory Position.fromBybitResponse(Map<String, dynamic> json) {
    return Position(
      id: json['positionIdx'],
      symbol: json['symbol'],
      side: json['side'],
      size: double.parse(json['size'].toString()),
      entryPrice: double.parse(json['avgPrice'].toString()),
      markPrice: double.parse(json['markPrice'].toString()),
      unrealisedPnl: double.parse(json['unrealisedPnl'].toString()),
      unrealisedPnlPercent: double.parse(json['unrealisedPnl'].toString()) /
          double.parse(json['positionValue'].toString()) *
          100,
      positionIdx: json['positionIdx'].toString(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(
          int.parse(json['createdTime'].toString())),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'symbol': symbol,
        'side': side,
        'size': size,
        'entryPrice': entryPrice,
        'markPrice': markPrice,
        'unrealisedPnl': unrealisedPnl,
        'unrealisedPnlPercent': unrealisedPnlPercent,
        'positionIdx': positionIdx,
        'createdAt': createdAt.toIso8601String(),
      };
}

// Settings 모델
class Settings {
  // 전역 설정 (관리자만 변경 가능)
  final String symbol;
  final int leverage;
  final double profitTarget; // 익절 %
  final double stopLoss; // 손절 %

  // 개인 설정 (일반 사용자 변경 가능)
  final int investmentRatio; // 투자 비율 %

  Settings({
    this.symbol = 'BTCUSDT',
    this.leverage = 5,
    this.profitTarget = 0.5,
    this.stopLoss = -0.5,
    this.investmentRatio = 25,
  });

  factory Settings.fromJson(Map<String, dynamic> json) {
    return Settings(
      symbol: json['symbol'] ?? 'BTCUSDT',
      leverage: json['leverage'] ?? 5,
      profitTarget: (json['profitTarget'] ?? 0.5).toDouble(),
      stopLoss: (json['stopLoss'] ?? -0.5).toDouble(),
      investmentRatio: json['investmentRatio'] ?? 25,
    );
  }

  Map<String, dynamic> toJson() => {
        'symbol': symbol,
        'leverage': leverage,
        'profitTarget': profitTarget,
        'stopLoss': stopLoss,
        'investmentRatio': investmentRatio,
      };

  Settings copyWith({
    String? symbol,
    int? leverage,
    double? profitTarget,
    double? stopLoss,
    int? investmentRatio,
  }) {
    return Settings(
      symbol: symbol ?? this.symbol,
      leverage: leverage ?? this.leverage,
      profitTarget: profitTarget ?? this.profitTarget,
      stopLoss: stopLoss ?? this.stopLoss,
      investmentRatio: investmentRatio ?? this.investmentRatio,
    );
  }
}

// 로그 엔트리 모델
class LogEntry {
  final String message;
  final DateTime timestamp;
  final LogLevel level;
  final Map<String, dynamic>? metadata;

  LogEntry({
    required this.message,
    required this.timestamp,
    required this.level,
    this.metadata,
  });

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      level: LogLevel.values.firstWhere(
        (e) => e.toString().split('.').last == json['level'],
        orElse: () => LogLevel.info,
      ),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() => {
        'message': message,
        'timestamp': timestamp.toIso8601String(),
        'level': level.toString().split('.').last,
        'metadata': metadata,
      };
}

enum LogLevel {
  info,
  warning,
  error,
  success,
  signal,
  trade,
}
