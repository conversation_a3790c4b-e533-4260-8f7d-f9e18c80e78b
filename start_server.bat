@echo off
REM start_server.bat - Open Systems Bot Named Pipe Server 시작 스크립트

echo ========================================
echo  Open Systems Bot - Named Pipe Server
echo ========================================
echo.

REM 관리자 권한 확인
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 관리자 권한으로 실행 중
) else (
    echo ❌ 관리자 권한이 필요합니다!
    echo    이 배치 파일을 마우스 우클릭 후 "관리자 권한으로 실행"을 선택해주세요.
    echo.
    pause
    exit /b 1
)

REM Dart 설치 확인
where dart >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Dart가 설치되지 않았거나 PATH에 없습니다!
    echo    Flutter SDK가 설치되어 있는지 확인해주세요.
    echo.
    pause
    exit /b 1
)

echo ✅ Dart 환경 확인 완료
echo.

REM 의존성 설치
echo 📦 의존성 설치 중...
call flutter pub get
if %errorLevel% neq 0 (
    echo ❌ 의존성 설치 실패!
    pause
    exit /b 1
)

echo ✅ 의존성 설치 완료
echo.

REM 서버 시작
echo 🚀 Named Pipe 서버 시작 중...
echo.
dart run bin/server.dart --monitor

REM 서버 종료 후
echo.
echo 서버가 종료되었습니다.
pause
