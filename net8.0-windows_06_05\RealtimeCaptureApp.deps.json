{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"RealtimeCaptureApp/1.0.0": {"dependencies": {"Extended.Wpf.Toolkit": "4.7.25104.5739", "Prism.DryIoc": "8.1.97", "System.Drawing.Common": "8.0.0"}, "runtime": {"RealtimeCaptureApp.dll": {}}}, "DryIoc.dll/4.7.7": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.3.0"}, "runtime": {"lib/netstandard2.0/DryIoc.dll": {"assemblyVersion": "4.7.7.0", "fileVersion": "4.7.7.0"}}}, "Extended.Wpf.Toolkit/4.7.25104.5739": {"runtime": {"lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Aero.dll": {"assemblyVersion": "4.7.0.0", "fileVersion": "4.7.0.0"}, "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Metro.dll": {"assemblyVersion": "4.7.0.0", "fileVersion": "4.7.0.0"}, "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.VS2010.dll": {"assemblyVersion": "4.7.0.0", "fileVersion": "4.7.0.0"}, "lib/net5.0/Xceed.Wpf.AvalonDock.dll": {"assemblyVersion": "4.7.0.0", "fileVersion": "4.7.0.0"}, "lib/net5.0/Xceed.Wpf.Toolkit.dll": {"assemblyVersion": "4.7.0.0", "fileVersion": "4.7.0.0"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.SystemEvents/8.0.0": {}, "Microsoft.Xaml.Behaviors.Wpf/1.1.31": {"runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.31.10800"}}}, "Prism.Core/8.1.97": {"runtime": {"lib/net5.0/Prism.dll": {"assemblyVersion": "8.1.97.5141", "fileVersion": "8.1.97.5141"}}}, "Prism.DryIoc/8.1.97": {"dependencies": {"DryIoc.dll": "4.7.7", "Prism.Wpf": "8.1.97"}, "runtime": {"lib/net5.0-windows7.0/Prism.DryIoc.Wpf.dll": {"assemblyVersion": "8.1.97.5141", "fileVersion": "8.1.97.5141"}}}, "Prism.Wpf/8.1.97": {"dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.31", "Prism.Core": "8.1.97"}, "runtime": {"lib/net5.0-windows7.0/Prism.Wpf.dll": {"assemblyVersion": "8.1.97.5141", "fileVersion": "8.1.97.5141"}}}, "System.Drawing.Common/8.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}}}, "libraries": {"RealtimeCaptureApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "DryIoc.dll/4.7.7": {"type": "package", "serviceable": true, "sha512": "sha512-GC2JdW0mN3wTcbcteP7LWwfU1eao0If/vMozCXSbcNZbHQD7kBjcKFO82g0OLssr7gGPWS4+vWpNZ1oOy54JKw==", "path": "dryioc.dll/4.7.7", "hashPath": "dryioc.dll.4.7.7.nupkg.sha512"}, "Extended.Wpf.Toolkit/4.7.25104.5739": {"type": "package", "serviceable": true, "sha512": "sha512-x6fCMLeExVZjyPl6f9/NRITW4uzT3xBZA40CBWA9YlH4/w6rKzsIeTDwYvavdmJLrl43p0LIFSQChISCnTSR6Q==", "path": "extended.wpf.toolkit/4.7.25104.5739", "hashPath": "extended.wpf.toolkit.4.7.25104.5739.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.31": {"type": "package", "serviceable": true, "sha512": "sha512-LZpuf82ACZWldmfMuv3CTUMDh3o0xo0uHUaybR5HgqVLDBJJ9RZLykplQ/bTJd0/VDt3EhD4iDgUgbdIUAM+Kg==", "path": "microsoft.xaml.behaviors.wpf/1.1.31", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.31.nupkg.sha512"}, "Prism.Core/8.1.97": {"type": "package", "serviceable": true, "sha512": "sha512-EP5zrvWddw3eSq25Y7hHnDYdmLZEC2Z/gMrvmHzUuLbitmA1UaS7wQUlSwNr9Km8lzJNCvytFnaGBEFukHgoHg==", "path": "prism.core/8.1.97", "hashPath": "prism.core.8.1.97.nupkg.sha512"}, "Prism.DryIoc/8.1.97": {"type": "package", "serviceable": true, "sha512": "sha512-8hdeAoZ+x1eBNIg+nMzox01uGUR4HnbCCR1iClNKwDfCWQ4ZtjEjsyeTXHcXKtG45xXqiERmJVew5tJmCdVPWw==", "path": "prism.dryioc/8.1.97", "hashPath": "prism.dryioc.8.1.97.nupkg.sha512"}, "Prism.Wpf/8.1.97": {"type": "package", "serviceable": true, "sha512": "sha512-ZEa6S1mK35h8/blyb0uR0ed3wkpHtPdhB4eniXINJnTiJMWlGl/As6SVlFFdOPD+qsEdWNYV3xgyQD/ue5cvBA==", "path": "prism.wpf/8.1.97", "hashPath": "prism.wpf.8.1.97.nupkg.sha512"}, "System.Drawing.Common/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JkbHJjtI/dWc5dfmEdJlbe3VwgZqCkZRtfuWFh5GOv0f+gGCfBtzMpIVkmdkj2AObO9y+oiOi81UGwH3aBYuqA==", "path": "system.drawing.common/8.0.0", "hashPath": "system.drawing.common.8.0.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}}}