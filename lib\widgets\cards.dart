// lib/widgets/cards.dart
import 'package:flutter/material.dart';
import '../models.dart';
import '../constants.dart';
import '../utils/helpers.dart';
import 'containers.dart';

// 봇 상태 카드
class BotStatusCard extends StatelessWidget {
  final bool isRunning;
  final bool isConnected;

  const BotStatusCard({
    super.key,
    required this.isRunning,
    required this.isConnected,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isRunning
                ? Colors.red.withValues(alpha: 0.5 * 255)
                : Colors.red.withValues(alpha: 0.5 * 255),
            width: 2,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '봇 상태:',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isRunning ? '자동매매 실행중' : '자동매매 중지됨',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[300],
                  ),
                ),
              ],
            ),
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isRunning ? Colors.red : Colors.red,
                boxShadow: isRunning
                    ? [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.5 * 255),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ]
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 자산 현황 카드
class AssetCard extends StatelessWidget {
  final double balance;
  final bool isConnected;

  const AssetCard({
    super.key,
    required this.balance,
    required this.isConnected,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor
                          .withValues(alpha: 0.08 * 255),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: AppConstants.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '자산 현황',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isConnected
                      ? Colors.red.withValues(alpha: 0.5 * 255)
                      : Colors.grey.withValues(alpha: 0.5 * 255),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: isConnected
                        ? Colors.red.withValues(alpha: 0.5 * 255)
                        : Colors.grey.withValues(alpha: 0.5 * 255),
                  ),
                ),
                child: Text(
                  isConnected ? 'API연결' : '연결끊김',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildAssetItem(
                  icon: Icons.account_balance_wallet,
                  label: '총 자산',
                  value: '\$${Helpers.formatNumber(balance)}',
                  color: AppConstants.primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildAssetItem(
                  icon: Icons.trending_up,
                  label: '수익률',
                  value: '+0.00%',
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAssetItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppConstants.borderColor,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

// 거래 설정 카드
class TradingSettingsCard extends StatefulWidget {
  final User user;
  final VoidCallback onStartBot;
  final bool isRunning;

  const TradingSettingsCard({
    super.key,
    required this.user,
    required this.onStartBot,
    required this.isRunning,
  });

  @override
  State<TradingSettingsCard> createState() => _TradingSettingsCardState();
}

class _TradingSettingsCardState extends State<TradingSettingsCard> {
  String _symbol = 'BTCUSDT';
  int _leverage = 5;
  int _investmentRatio = 25;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    // TODO: 설정 로드
  }

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '거래 설정',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 20),

          // 거래 페어
          _buildSettingItem(
            label: '거래 페어',
            icon: _getSymbolIcon(_symbol),
            child: DropdownButton<String>(
              value: _symbol,
              dropdownColor: AppConstants.cardColor,
              style: const TextStyle(color: Colors.white),
              underline: const SizedBox(),
              onChanged: widget.user.isAdmin && !widget.isRunning
                  ? (value) => setState(() => _symbol = value!)
                  : null,
              items: AppConstants.symbols.map((symbol) {
                return DropdownMenuItem(
                  value: symbol,
                  child: Text(symbol),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 16),

          // 레버리지
          _buildSettingItem(
            label: '레버리지',
            icon: Icons.trending_up,
            child: DropdownButton<int>(
              value: _leverage,
              dropdownColor: AppConstants.cardColor,
              style: const TextStyle(color: Colors.white),
              underline: const SizedBox(),
              onChanged: widget.user.isAdmin && !widget.isRunning
                  ? (value) => setState(() => _leverage = value!)
                  : null,
              items: AppConstants.leverageOptions.map((leverage) {
                return DropdownMenuItem(
                  value: leverage,
                  child: Text('${leverage}X'),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 16),

          // 투자 수량
          _buildSettingItem(
            label: '투자 수량',
            icon: Icons.pie_chart,
            child: DropdownButton<int>(
              value: _investmentRatio,
              dropdownColor: AppConstants.cardColor,
              style: const TextStyle(color: Colors.white),
              underline: const SizedBox(),
              onChanged: !widget.isRunning
                  ? (value) => setState(() => _investmentRatio = value!)
                  : null,
              items: AppConstants.investmentRatios.map((ratio) {
                return DropdownMenuItem(
                  value: ratio,
                  child: Text('$ratio%'),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 24),

          // 버튼
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: !widget.isRunning ? _saveSettings : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        AppConstants.primaryColor.withValues(alpha: 0.08 * 255),
                    foregroundColor: AppConstants.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: AppConstants.primaryColor
                            .withValues(alpha: 0.3 * 255),
                      ),
                    ),
                  ),
                  child: const Text(
                    '설정 저장',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: widget.onStartBot,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.isRunning
                        ? Colors.red.withValues(alpha: 0.2 * 255)
                        : Colors.green.withValues(alpha: 0.2 * 255),
                    foregroundColor:
                        widget.isRunning ? Colors.red : Colors.green,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: widget.isRunning
                            ? Colors.red.withValues(alpha: 0.3 * 255)
                            : Colors.green.withValues(alpha: 0.3 * 255),
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(widget.isRunning ? Icons.stop : Icons.play_arrow),
                      const SizedBox(width: 8),
                      Text(
                        widget.isRunning ? '봇 중지' : '봇 시작',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required String label,
    required IconData icon,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppConstants.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppConstants.borderColor,
            ),
          ),
          child: Row(
            children: [
              Icon(icon, color: AppConstants.primaryColor, size: 20),
              const SizedBox(width: 12),
              Expanded(child: child),
              Icon(
                Icons.arrow_drop_down,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getSymbolIcon(String symbol) {
    switch (symbol) {
      case 'BTCUSDT':
        return Icons.currency_bitcoin;
      case 'ETHUSDT':
        return Icons.account_balance_wallet;
      default:
        return Icons.monetization_on;
    }
  }

  Future<void> _saveSettings() async {
    // TODO: 설정 저장
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('설정이 저장되었습니다'),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }
}

// 포지션 신호정보 카드
class SignalInfoCard extends StatelessWidget {
  final Signal? lastSignal;
  final List<Position> positions;

  const SignalInfoCard({
    super.key,
    required this.lastSignal,
    required this.positions,
  });

  @override
  Widget build(BuildContext context) {
    final hasActivePosition = positions.isNotEmpty;

    return GlassContainer(
      child: Row(
        children: [
          Icon(
            Icons.signal_cellular_alt,
            color: AppConstants.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          const Text(
            '포지션 신호정보:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          const Spacer(),
          Text(
            hasActivePosition
                ? '${positions.first.side == 'Buy' ? '롱' : '숏'} 포지션 활성화'
                : '신호 대기중',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[300],
            ),
          ),
        ],
      ),
    );
  }
}

// 회원 랭킹 리스트
class RankingCard extends StatelessWidget {
  const RankingCard({super.key});

  @override
  Widget build(BuildContext context) {
    final rankings = [
      {'rank': 1, 'name': 'CryptoMaster', 'profit': '+2,458%', 'trades': 1234},
      {'rank': 2, 'name': 'BTCWhale', 'profit': '+1,892%', 'trades': 987},
      {'rank': 3, 'name': 'TradingPro', 'profit': '+1,456%', 'trades': 1567},
      {'rank': 4, 'name': 'AlgoTrader', 'profit': '+998%', 'trades': 2341},
      {'rank': 5, 'name': 'SmartMoney', 'profit': '+756%', 'trades': 876},
    ];

    return GlassContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: AppConstants.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                '회원 랭킹 리스트',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...rankings.map((user) => _buildRankingItem(user)).toList(),
        ],
      ),
    );
  }

  Widget _buildRankingItem(Map<String, dynamic> user) {
    final isTop3 = user['rank'] <= 3;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppConstants.borderColor,
        ),
      ),
      child: Row(
        children: [
          Text(
            '#${user['rank']}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isTop3 ? Colors.yellow : Colors.grey[400],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              user['name'],
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                user['profit'],
                style: TextStyle(
                  color: user['profit'].startsWith('+')
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${user['trades']} 거래',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// 로그 카드
class LogCard extends StatelessWidget {
  final List<LogEntry> logs;
  final ScrollController scrollController;
  final VoidCallback onClear;

  const LogCard({
    super.key,
    required this.logs,
    required this.scrollController,
    required this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return GlassContainer(
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.terminal,
                color: AppConstants.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '로그',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: onClear,
                icon: Icon(
                  Icons.clear_all,
                  color: Colors.grey[400],
                  size: 20,
                ),
                tooltip: '로그 지우기',
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            height: 150,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppConstants.backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppConstants.borderColor,
              ),
            ),
            child: logs.isEmpty
                ? Center(
                    child: Text(
                      '로그가 없습니다',
                      style: TextStyle(
                        color: Colors.grey[500],
                      ),
                    ),
                  )
                : ListView.builder(
                    controller: scrollController,
                    itemCount: logs.length,
                    itemBuilder: (context, index) {
                      final log = logs[index];
                      return _buildLogItem(log);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogItem(LogEntry log) {
    Color color;
    IconData icon;
    switch (log.level) {
      case LogLevel.info:
        color = Colors.blue;
        icon = Icons.info_outline;
        break;
      case LogLevel.warning:
        color = Colors.orange;
        icon = Icons.warning_amber;
        break;
      case LogLevel.error:
        color = Colors.red;
        icon = Icons.error_outline;
        break;
      case LogLevel.success:
        color = Colors.green;
        icon = Icons.check_circle_outline;
        break;
      case LogLevel.signal:
        color = Colors.purple;
        icon = Icons.signal_cellular_alt;
        break;
      case LogLevel.trade:
        color = Colors.cyan;
        icon = Icons.trending_up;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Text(
            Helpers.formatTime(log.timestamp),
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              log.message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 신호 출력 표시 카드
class SignalDisplayCard extends StatelessWidget {
  final Signal signal;

  const SignalDisplayCard({
    super.key,
    required this.signal,
  });

  @override
  Widget build(BuildContext context) {
    final isLong = signal.type == 'long';

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.5 * 255),
          width: 2,
        ),
      ),
      child: GlassContainer(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.green.withValues(alpha: 0.5 * 255),
              width: 2,
            ),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.2 * 255),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isLong ? Icons.trending_up : Icons.trending_down,
                      color: Colors.green,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '신호 출력 표시',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.3 * 255),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      isLong ? '롱 포지션' : '숏 포지션',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '신호 시간: ${Helpers.formatTime(signal.timestamp)}',
                      style: TextStyle(
                        color: Colors.grey[300],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                '최근 검출된 신호입니다. 중복 신호는 무시합니다.',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
