// lib/models/user.dart
class User {
  final String email;
  final String name;
  final String passwordHash;
  final String encryptedApiKey;
  final String encryptedApiSecret;
  final bool isAdmin;
  final DateTime createdAt;
  final Map<String, dynamic> personalSettings;

  User({
    required this.email,
    required this.name,
    required this.passwordHash,
    this.encryptedApiKey = '',
    this.encryptedApiSecret = '',
    this.isAdmin = false,
    required this.createdAt,
    this.personalSettings = const {},
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      email: json['email'],
      name: json['name'],
      passwordHash: json['passwordHash'],
      encryptedApiKey: json['encryptedApiKey'] ?? '',
      encryptedApiSecret: json['encryptedApiSecret'] ?? '',
      isAdmin: json['isAdmin'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      personalSettings: json['personalSettings'] ?? {},
    );
  }

  Map<String, dynamic> toJson() => {
        'email': email,
        'name': name,
        'passwordHash': passwordHash,
        'encryptedApiKey': encryptedApiKey,
        'encryptedApiSecret': encryptedApiSecret,
        'isAdmin': isAdmin,
        'createdAt': createdAt.toIso8601String(),
        'personalSettings': personalSettings,
      };

  User copyWith({
    String? email,
    String? name,
    String? passwordHash,
    String? encryptedApiKey,
    String? encryptedApiSecret,
    bool? isAdmin,
    DateTime? createdAt,
    Map<String, dynamic>? personalSettings,
  }) {
    return User(
      email: email ?? this.email,
      name: name ?? this.name,
      passwordHash: passwordHash ?? this.passwordHash,
      encryptedApiKey: encryptedApiKey ?? this.encryptedApiKey,
      encryptedApiSecret: encryptedApiSecret ?? this.encryptedApiSecret,
      isAdmin: isAdmin ?? this.isAdmin,
      createdAt: createdAt ?? this.createdAt,
      personalSettings: personalSettings ?? this.personalSettings,
    );
  }
}
