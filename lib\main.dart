// lib/main.dart
import 'package:flutter/material.dart';
import 'screens.dart';
import 'constants.dart';
import 'server/pipe_server.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 서버 모드인지 확인
  const bool isServer =
      bool.fromEnvironment('SERVER_MODE', defaultValue: false);

  if (isServer) {
    // Named Pipe 서버 실행
    await PipeServer.start();
  } else {
    // 웹 애플리케이션 실행
    runApp(const OpenSystemsBot());
  }
}

class OpenSystemsBot extends StatelessWidget {
  const OpenSystemsBot({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Open Systems_Bot',
      theme: AppConstants.darkTheme,
      home: const LoginScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
