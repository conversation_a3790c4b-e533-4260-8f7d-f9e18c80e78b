// lib/services/pipe_listener.dart
import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/signal.dart';

class PipeListener {
  WebSocketChannel? _channel;
  final _signalController = StreamController<Signal>.broadcast();

  Stream<Signal> get signalStream => _signalController.stream;

  bool _isConnected = false;
  String? _lastProcessedSignalId;
  Timer? _reconnectTimer;

  bool get isConnected => _isConnected;

  // WebSocket 서버에 연결 (Flutter 내장 서버)
  Future<void> connect() async {
    try {
      _channel = WebSocketChannel.connect(
        Uri.parse('ws://localhost:8080/pipe'),
      );

      _isConnected = true;

      _channel!.stream.listen(
        _processMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
    } catch (e) {
      print('Failed to connect to pipe server: $e');
      _scheduleReconnect();
    }
  }

  void _processMessage(dynamic message) {
    try {
      // JSON 형태의 신호 메시지 처리
      if (message is String && message.startsWith('{')) {
        final data = jsonDecode(message);

        if (data['type'] == 'signal') {
          final signalId = data['id'];
          final signalType = data['signalType'];

          // 중복 신호 방지
          if (signalId != null &&
              signalId != _lastProcessedSignalId &&
              signalType != null) {
            _lastProcessedSignalId = signalId;

            final signal = Signal(
              id: signalId,
              type: signalType,
              timestamp: DateTime.parse(data['timestamp']),
              source: 'pipe',
              metadata: {
                'originalMessage': data['originalMessage'],
                'serverProcessed': true,
              },
            );

            _signalController.add(signal);
            print('신호 수신: ${signal.type} (ID: ${signal.id})');
          }
        } else if (data['type'] == 'connected') {
          print('서버 연결 확인: ${data['message']}');
        }
      } else {
        // 원시 로그 메시지 처리 (기존 방식)
        final String logLine = message.toString();

        if (_isSignalLine(logLine)) {
          final signalId = _extractSignalId(logLine);
          final signalType = _extractSignalType(logLine);

          if (signalId != null &&
              signalId != _lastProcessedSignalId &&
              signalType != null) {
            _lastProcessedSignalId = signalId;

            final signal = Signal(
              id: signalId,
              type: signalType,
              timestamp: DateTime.now(),
              source: 'pipe',
              metadata: {'rawLog': logLine},
            );

            _signalController.add(signal);
          }
        }
      }
    } catch (e) {
      print('Error processing message: $e');
    }
  }

  bool _isSignalLine(String line) {
    const patterns = [
      '롱 신호가 감지되었습니다',
      '숏 신호가 감지되었습니다',
      'Long signal detected',
      'Short signal detected',
    ];

    return patterns.any((pattern) => line.contains(pattern));
  }

  String? _extractSignalId(String line) {
    // 타임스탬프를 ID로 사용
    final timestampRegex = RegExp(r'(\d{2}:\d{2}:\d{2})');
    final match = timestampRegex.firstMatch(line);
    return match?.group(1) ?? DateTime.now().millisecondsSinceEpoch.toString();
  }

  String? _extractSignalType(String line) {
    if (line.contains('롱') || line.contains('Long')) {
      return 'long';
    } else if (line.contains('숏') || line.contains('Short')) {
      return 'short';
    }
    return null;
  }

  void _handleError(error) {
    print('WebSocket error: $error');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _handleDisconnection() {
    print('WebSocket disconnected');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(const Duration(seconds: 5), () {
      print('Attempting to reconnect...');
      connect();
    });
  }

  // 서버 통계 요청
  Future<Map<String, dynamic>?> getServerStats() async {
    if (!_isConnected || _channel == null) return null;

    try {
      final completer = Completer<Map<String, dynamic>?>();
      late StreamSubscription subscription;

      // 응답 대기
      subscription = _channel!.stream.listen((message) {
        try {
          if (message is String && message.startsWith('{')) {
            final data = jsonDecode(message);
            if (data['type'] == 'stats') {
              subscription.cancel();
              completer.complete(data['data']);
              return;
            }
          }
        } catch (e) {
          // 다른 메시지는 무시
        }
      });

      // 통계 요청 전송
      _channel!.sink.add(jsonEncode({
        'type': 'getStats',
        'timestamp': DateTime.now().toIso8601String(),
      }));

      // 5초 타임아웃
      Timer(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          subscription.cancel();
          completer.complete(null);
        }
      });

      return await completer.future;
    } catch (e) {
      print('Error getting server stats: $e');
      return null;
    }
  }

  // 연결 테스트 (ping)
  Future<bool> ping() async {
    if (!_isConnected || _channel == null) return false;

    try {
      final completer = Completer<bool>();
      late StreamSubscription subscription;

      subscription = _channel!.stream.listen((message) {
        try {
          if (message is String && message.startsWith('{')) {
            final data = jsonDecode(message);
            if (data['type'] == 'pong') {
              subscription.cancel();
              completer.complete(true);
              return;
            }
          }
        } catch (e) {
          // 다른 메시지는 무시
        }
      });

      _channel!.sink.add(jsonEncode({
        'type': 'ping',
        'timestamp': DateTime.now().toIso8601String(),
      }));

      Timer(const Duration(seconds: 3), () {
        if (!completer.isCompleted) {
          subscription.cancel();
          completer.complete(false);
        }
      });

      return await completer.future;
    } catch (e) {
      return false;
    }
  }

  // 연결 상태 정보
  Map<String, dynamic> getConnectionInfo() {
    return {
      'isConnected': _isConnected,
      'lastProcessedSignalId': _lastProcessedSignalId,
      'hasActiveListeners': _signalController.hasListener,
      'serverUrl': 'ws://localhost:8080/pipe',
    };
  }

  void disconnect() {
    _reconnectTimer?.cancel();
    _channel?.sink.close();
    _isConnected = false;
  }

  void dispose() {
    disconnect();
    _signalController.close();
  }
}
