// lib/services/pipe_listener.dart
import 'dart:async';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../models/signal.dart';

class PipeListener {
  WebSocketChannel? _channel;
  final _signalController = StreamController<Signal>.broadcast();
  
  Stream<Signal> get signalStream => _signalController.stream;
  
  bool _isConnected = false;
  String? _lastProcessedSignalId;
  Timer? _reconnectTimer;

  bool get isConnected => _isConnected;

  // WebSocket 서버에 연결 (Flutter 내장 서버)
  Future<void> connect() async {
    try {
      _channel = WebSocketChannel.connect(
        Uri.parse('ws://localhost:8080/pipe'),
      );
      
      _isConnected = true;
      
      _channel!.stream.listen(
        _processMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
    } catch (e) {
      print('Failed to connect to pipe server: $e');
      _scheduleReconnect();
    }
  }

  void _processMessage(dynamic message) {
    try {
      final String logLine = message.toString();
      
      // 신호 패턴 검사
      if (_isSignalLine(logLine)) {
        final signalId = _extractSignalId(logLine);
        final signalType = _extractSignalType(logLine);
        
        // 중복 신호 방지
        if (signalId != null && 
            signalId != _lastProcessedSignalId && 
            signalType != null) {
          
          _lastProcessedSignalId = signalId;
          
          final signal = Signal(
            id: signalId,
            type: signalType,
            timestamp: DateTime.now(),
            source: 'pipe',
            metadata: {'rawLog': logLine},
          );
          
          _signalController.add(signal);
        }
      }
    } catch (e) {
      print('Error processing message: $e');
    }
  }

  bool _isSignalLine(String line) {
    const patterns = [
      '롱 신호가 감지되었습니다',
      '숏 신호가 감지되었습니다',
      'Long signal detected',
      'Short signal detected',
    ];
    
    return patterns.any((pattern) => line.contains(pattern));
  }

  String? _extractSignalId(String line) {
    // 타임스탬프를 ID로 사용
    final timestampRegex = RegExp(r'(\d{2}:\d{2}:\d{2})');
    final match = timestampRegex.firstMatch(line);
    return match?.group(1) ?? DateTime.now().millisecondsSinceEpoch.toString();
  }

  String? _extractSignalType(String line) {
    if (line.contains('롱') || line.contains('Long')) {
      return 'long';
    } else if (line.contains('숏') || line.contains('Short')) {
      return 'short';
    }
    return null;
  }

  void _handleError(error) {
    print('WebSocket error: $error');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _handleDisconnection() {
    print('WebSocket disconnected');
    _isConnected = false;
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(const Duration(seconds: 5), () {
      print('Attempting to reconnect...');
      connect();
    });
  }

  void disconnect() {
    _reconnectTimer?.cancel();
    _channel?.sink.close();
    _isConnected = false;
  }

  void dispose() {
    disconnect();
    _signalController.close();
  }
}