// lib/models/settings.dart
class Settings {
  // 전역 설정 (관리자만 변경 가능)
  final String symbol;
  final int leverage;
  final double profitTarget; // 익절 %
  final double stopLoss; // 손절 %
  
  // 개인 설정 (일반 사용자 변경 가능)
  final int investmentRatio; // 투자 비율 %

  Settings({
    this.symbol = 'BTCUSDT',
    this.leverage = 5,
    this.profitTarget = 0.5,
    this.stopLoss = -0.5,
    this.investmentRatio = 25,
  });

  factory Settings.fromJson(Map<String, dynamic> json) {
    return Settings(
      symbol: json['symbol'] ?? 'BTCUSDT',
      leverage: json['leverage'] ?? 5,
      profitTarget: (json['profitTarget'] ?? 0.5).toDouble(),
      stopLoss: (json['stopLoss'] ?? -0.5).toDouble(),
      investmentRatio: json['investmentRatio'] ?? 25,
    );
  }

  Map<String, dynamic> toJson() => {
    'symbol': symbol,
    'leverage': leverage,
    'profitTarget': profitTarget,
    'stopLoss': stopLoss,
    'investmentRatio': investmentRatio,
  };

  Settings copyWith({
    String? symbol,
    int? leverage,
    double? profitTarget,
    double? stopLoss,
    int? investmentRatio,
  }) {
    return Settings(
      symbol: symbol ?? this.symbol,
      leverage: leverage ?? this.leverage,
      profitTarget: profitTarget ?? this.profitTarget,
      stopLoss: stopLoss ?? this.stopLoss,
      investmentRatio: investmentRatio ?? this.investmentRatio,
    );
  }
}