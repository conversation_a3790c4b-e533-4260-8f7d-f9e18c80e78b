#!/usr/bin/env dart

// bin/server.dart
import 'dart:io';
import 'dart:async';
import '../lib/server/pipe_server.dart';

void main(List<String> arguments) async {
  print('🚀 Open Systems Bot - Named Pipe Server');
  print('========================================');

  // 관리자 권한 확인 (Windows)
  if (Platform.isWindows) {
    print('⚠️  Windows에서는 관리자 권한으로 실행해주세요.');
    print('   Named Pipe 접근을 위해 필요합니다.');
  }

  try {
    // 서버 시작
    await PipeServer.start();

    print('✅ 서버가 성공적으로 시작되었습니다!');
    print('📡 WebSocket: ws://localhost:8080/pipe');
    print('📊 통계: http://localhost:8080/stats');
    print('🔗 Named Pipe: \\\\.\\pipe\\RealtimeCaptureLogPipe');
    print('');
    print('종료하려면 Ctrl+C를 누르세요...');

    // 종료 신호 처리
    ProcessSignal.sigint.watch().listen((signal) async {
      print('\n🛑 서버 종료 중...');
      await PipeServer.stop();
      print('✅ 서버가 정상적으로 종료되었습니다.');
      exit(0);
    });

    // 서버 상태 모니터링 (선택사항)
    if (arguments.contains('--monitor')) {
      _startMonitoring();
    }
  } catch (e) {
    print('❌ 서버 시작 실패: $e');
    exit(1);
  }
}

void _startMonitoring() {
  print('📊 모니터링 모드 활성화');

  // 30초마다 상태 출력
  Timer.periodic(Duration(seconds: 30), (timer) {
    print('⏰ ${DateTime.now().toString().substring(11, 19)} - 서버 실행 중...');
  });
}
