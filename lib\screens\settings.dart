// lib/screens/settings.dart
import 'package:flutter/material.dart';
import '../models.dart';
import '../services.dart';
import '../constants.dart';
import '../widgets.dart';

class SettingsScreen extends StatefulWidget {
  final User user;

  const SettingsScreen({
    super.key,
    required this.user,
  });

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final DatabaseService _db = DatabaseService();
  Settings? _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    await _db.init();
    final settings = await _db.getSettings();
    setState(() {
      _settings = settings;
      _isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    if (_settings == null) return;

    await _db.saveSettings(_settings!);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('설정이 저장되었습니다'),
          backgroundColor: AppConstants.primaryColor,
        ),
      );
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('봇 설정'),
        backgroundColor: AppConstants.cardColor,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: GlassContainer(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '전역 설정',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '관리자만 변경 가능한 설정입니다',
                      style: TextStyle(
                        color: Colors.grey[400],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // 거래 페어
                    _buildDropdownField(
                      label: '거래 페어',
                      value: _settings!.symbol,
                      items: AppConstants.symbols,
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings!.copyWith(symbol: value);
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // 레버리지
                    _buildDropdownField(
                      label: '레버리지',
                      value: _settings!.leverage.toString(),
                      items: AppConstants.leverageOptions
                          .map((e) => e.toString())
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _settings =
                              _settings!.copyWith(leverage: int.parse(value!));
                        });
                      },
                      suffix: 'X',
                    ),
                    const SizedBox(height: 16),

                    // 익절 %
                    _buildSliderField(
                      label: '익절 목표',
                      value: _settings!.profitTarget,
                      min: 0.1,
                      max: 5.0,
                      divisions: 49,
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings!.copyWith(profitTarget: value);
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // 손절 %
                    _buildSliderField(
                      label: '손절 기준',
                      value: _settings!.stopLoss.abs(),
                      min: 0.1,
                      max: 5.0,
                      divisions: 49,
                      onChanged: (value) {
                        setState(() {
                          _settings = _settings!.copyWith(stopLoss: -value);
                        });
                      },
                      isNegative: true,
                    ),
                    const SizedBox(height: 32),

                    // 저장 버튼
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _saveSettings,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryColor
                              .withValues(alpha: 0.08 * 255),
                          foregroundColor: AppConstants.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: AppConstants.primaryColor
                                  .withValues(alpha: 0.3 * 255),
                            ),
                          ),
                        ),
                        child: const Text(
                          '설정 저장',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[300],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05 * 255),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1 * 255),
            ),
          ),
          child: DropdownButton<String>(
            value: value,
            isExpanded: true,
            dropdownColor: AppConstants.cardColor,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            underline: const SizedBox(),
            onChanged: widget.user.isAdmin ? onChanged : null,
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(suffix != null ? '$item$suffix' : item),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSliderField({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
    bool isNegative = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Colors.grey[300],
                fontSize: 14,
              ),
            ),
            Text(
              '${isNegative ? '-' : '+'}${value.toStringAsFixed(1)}%',
              style: TextStyle(
                color: isNegative ? Colors.red : Colors.green,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          activeColor: isNegative ? Colors.red : Colors.green,
          onChanged: widget.user.isAdmin ? onChanged : null,
        ),
      ],
    );
  }
}
