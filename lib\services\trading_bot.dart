// lib/services/trading_bot.dart
import 'dart:async';
import '../models/position.dart';
import '../models/signal.dart';
import '../models/settings.dart';
import '../models/user.dart';
import 'bybit_api.dart';
import 'database.dart';
import 'pipe_listener.dart';

class TradingBot {
  final DatabaseService _db = DatabaseService();
  final PipeListener _pipeListener = PipeListener();
  
  BybitApiService? _api;
  Timer? _positionCheckTimer;
  Timer? _balanceUpdateTimer;
  
  bool _isRunning = false;
  Settings? _settings;
  User? _currentUser;
  
  // 상태
  double _walletBalance = 0.0;
  List<Position> _currentPositions = [];
  Signal? _lastSignal;
  
  // Getters
  bool get isRunning => _isRunning;
  double get walletBalance => _walletBalance;
  List<Position> get currentPositions => _currentPositions;
  Signal? get lastSignal => _lastSignal;

  // 봇 초기화
  Future<void> initialize(User user, String apiKey, String apiSecret) async {
    _currentUser = user;
    _settings = await _db.getSettings();
    
    _api = BybitApiService(
      apiKey: apiKey,
      apiSecret: apiSecret,
      isTestnet: false,
    );
    
    // Named Pipe 리스너 연결
    await _pipeListener.connect();
    
    // 신호 구독
    _pipeListener.signalStream.listen(_handleSignal);
  }

  // 봇 시작
  Future<void> start() async {
    if (_isRunning || _api == null) return;
    
    _isRunning = true;
    
    // 레버리지 설정
    await _api!.setLeverage(
      symbol: _settings!.symbol,
      leverage: _settings!.leverage.toString(),
    );
    
    // 초기 잔고 업데이트
    await _updateBalance();
    
    // 포지션 체크 타이머 시작 (0.5초마다)
    _positionCheckTimer = Timer.periodic(
      const Duration(milliseconds: 500),
      (_) => _checkPositions(),
    );
    
    // 잔고 업데이트 타이머 (30초마다)
    _balanceUpdateTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _updateBalance(),
    );
    
    // 로그 저장
    await _saveLog('봇 시작됨', 'info');
  }

  // 봇 중지
  Future<void> stop() async {
    _isRunning = false;
    
    _positionCheckTimer?.cancel();
    _balanceUpdateTimer?.cancel();
    
    await _saveLog('봇 중지됨', 'warning');
  }

  // 신호 처리
  Future<void> _handleSignal(Signal signal) async {
    if (!_isRunning) return;
    
    _lastSignal = signal;
    
    // 신호 저장
    await _db.saveSignal(signal);
    
    // 로그
    await _saveLog(
      '${signal.type == 'long' ? '롱' : '숏'} 신호 감지',
      'signal',
    );
    
    // 양방향 포지션 진입
    await _enterBidirectionalPosition();
  }

  // 양방향 포지션 진입
  Future<void> _enterBidirectionalPosition() async {
    try {
      final currentPrice = await _api!.getCurrentPrice(_settings!.symbol);
      
      // 투자 금액 계산
      final investmentAmount = _walletBalance * (_settings!.investmentRatio / 100);
      final positionValue = investmentAmount / 2; // 롱/숏 각 50%
      final qty = (positionValue * _settings!.leverage) / currentPrice;
      
      // 롱 포지션 진입 (지정가)
      await _api!.placeLimitOrder(
        symbol: _settings!.symbol,
        side: 'Buy',
        qty: qty,
        price: currentPrice * 0.9995, // 슬리피지 0.05%
        positionIdx: '1',
      );
      
      // 숏 포지션 진입 (지정가)
      await _api!.placeLimitOrder(
        symbol: _settings!.symbol,
        side: 'Sell',
        qty: qty,
        price: currentPrice * 1.0005, // 슬리피지 0.05%
        positionIdx: '2',
      );
      
      await _saveLog(
        '양방향 포지션 진입 - 수량: $qty, 가격: $currentPrice',
        'trade',
      );
    } catch (e) {
      await _saveLog('포지션 진입 실패: $e', 'error');
    }
  }

  // 포지션 체크 및 관리
  Future<void> _checkPositions() async {
    if (!_isRunning || _api == null) return;
    
    try {
      _currentPositions = await _api!.getPositions(_settings!.symbol);
      
      for (final position in _currentPositions) {
        // 손절: -0.5% 도달시 즉시 청산
        if (position.unrealisedPnlPercent <= _settings!.stopLoss) {
          await _closePosition(position, '손절');
          
          // 반대 포지션 진입
          await _reversePosition(position.side);
        }
        // 익절: +0.5% 도달시 신호 대기
        else if (position.unrealisedPnlPercent >= _settings!.profitTarget) {
          // 신호가 있고 반대 방향이면 청산
          if (_lastSignal != null && _shouldCloseBySignal(position)) {
            await _closePosition(position, '익절');
          }
        }
      }
    } catch (e) {
      print('Position check error: $e');
    }
  }

  // 신호에 따른 청산 여부 판단
  bool _shouldCloseBySignal(Position position) {
    if (_lastSignal == null) return false;
    
    // 롱 포지션인데 숏 신호가 온 경우
    if (position.side == 'Buy' && _lastSignal!.type == 'short') return true;
    
    // 숏 포지션인데 롱 신호가 온 경우
    if (position.side == 'Sell' && _lastSignal!.type == 'long') return true;
    
    return false;
  }

  // 포지션 청산
  Future<void> _closePosition(Position position, String reason) async {
    try {
      // 시장가로 즉시 청산
      await _api!.placeMarketOrder(
        symbol: position.symbol,
        side: position.side == 'Buy' ? 'Sell' : 'Buy',
        qty: position.size,
        positionIdx: position.positionIdx,
      );
      
      // 거래 기록 저장
      await _db.saveTrade({
        'symbol': position.symbol,
        'side': position.side,
        'qty': position.size,
        'entryPrice': position.entryPrice,
        'exitPrice': position.markPrice,
        'pnl': position.unrealisedPnl,
        'pnlPercent': position.unrealisedPnlPercent,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      await _saveLog(
        '포지션 청산 ($reason) - ${position.side} ${position.size} @ ${position.markPrice}',
        'trade',
      );
    } catch (e) {
      await _saveLog('포지션 청산 실패: $e', 'error');
    }
  }

  // 반대 포지션 진입
  Future<void> _reversePosition(String closedSide) async {
    try {
      final currentPrice = await _api!.getCurrentPrice(_settings!.symbol);
      
      // 투자 금액 계산
      final investmentAmount = _walletBalance * (_settings!.investmentRatio / 100);
      final positionValue = investmentAmount / 2;
      final qty = (positionValue * _settings!.leverage) / currentPrice;
      
      if (closedSide == 'Buy') {
        // 숏 포지션 진입
        await _api!.placeLimitOrder(
          symbol: _settings!.symbol,
          side: 'Sell',
          qty: qty,
          price: currentPrice * 1.0005,
          positionIdx: '2',
        );
      } else {
        // 롱 포지션 진입
        await _api!.placeLimitOrder(
          symbol: _settings!.symbol,
          side: 'Buy',
          qty: qty,
          price: currentPrice * 0.9995,
          positionIdx: '1',
        );
      }
      
      await _saveLog(
        '반대 포지션 진입 - ${closedSide == 'Buy' ? 'Sell' : 'Buy'} $qty @ $currentPrice',
        'trade',
      );
    } catch (e) {
      await _saveLog('반대 포지션 진입 실패: $e', 'error');
    }
  }

  // 잔고 업데이트
  Future<void> _updateBalance() async {
    if (_api == null) return;
    
    try {
      _walletBalance = await _api!.getWalletBalance();
    } catch (e) {
      print('Balance update error: $e');
    }
  }

  // 로그 저장
  Future<void> _saveLog(String message, String level) async {
    await _db.saveLog({
      'timestamp': DateTime.now().toIso8601String(),
      'level': level,
      'message': message,
      'user': _currentUser?.email ?? 'system',
    });
  }

  // 정리
  void dispose() {
    stop();
    _pipeListener.dispose();
  }
}