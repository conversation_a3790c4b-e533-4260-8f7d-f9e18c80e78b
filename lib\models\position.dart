// lib/models/position.dart
class Position {
  final String id;
  final String symbol;
  final String side; // 'Buy' or 'Sell'
  final double size;
  final double entryPrice;
  final double markPrice;
  final double unrealisedPnl;
  final double unrealisedPnlPercent;
  final String positionIdx; // '1' for Buy, '2' for Sell
  final DateTime createdAt;

  Position({
    required this.id,
    required this.symbol,
    required this.side,
    required this.size,
    required this.entryPrice,
    required this.markPrice,
    required this.unrealisedPnl,
    required this.unrealisedPnlPercent,
    required this.positionIdx,
    required this.createdAt,
  });

  factory Position.fromBybitResponse(Map<String, dynamic> json) {
    return Position(
      id: json['positionIdx'],
      symbol: json['symbol'],
      side: json['side'],
      size: double.parse(json['size'].toString()),
      entryPrice: double.parse(json['avgPrice'].toString()),
      markPrice: double.parse(json['markPrice'].toString()),
      unrealisedPnl: double.parse(json['unrealisedPnl'].toString()),
      unrealisedPnlPercent: double.parse(json['unrealisedPnl'].toString()) /
          double.parse(json['positionValue'].toString()) *
          100,
      positionIdx: json['positionIdx'].toString(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(
          int.parse(json['createdTime'].toString())),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'symbol': symbol,
        'side': side,
        'size': size,
        'entryPrice': entryPrice,
        'markPrice': markPrice,
        'unrealisedPnl': unrealisedPnl,
        'unrealisedPnlPercent': unrealisedPnlPercent,
        'positionIdx': positionIdx,
        'createdAt': createdAt.toIso8601String(),
      };
}
