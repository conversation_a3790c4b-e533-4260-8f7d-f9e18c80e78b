// lib/server/pipe_server.dart
import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/io.dart';

class PipeServer {
  static HttpServer? _httpServer;
  static final List<IOWebSocketChannel> _clients = [];
  static File? _pipeFile;
  static StreamSubscription? _pipeSubscription;

  static Future<void> start() async {
    print('Starting Named Pipe server...');

    // HTTP/WebSocket 서버 시작
    _httpServer = await HttpServer.bind('localhost', 8080);
    print('Server listening on ws://localhost:8080');

    // Named Pipe 연결
    await _connectToNamedPipe();

    // WebSocket 연결 처리
    _httpServer!.listen((HttpRequest request) {
      if (request.uri.path == '/pipe' &&
          WebSocketTransformer.isUpgradeRequest(request)) {
        WebSocketTransformer.upgrade(request).then(_handleWebSocket);
      } else {
        request.response
          ..statusCode = HttpStatus.notFound
          ..write('Not found')
          ..close();
      }
    });
  }

  static Future<void> _connectToNamedPipe() async {
    try {
      const pipePath = r'\\.\pipe\RealtimeCaptureLogPipe';
      _pipeFile = File(pipePath);

      // Named Pipe 읽기 스트림
      _pipeSubscription = _pipeFile!
          .openRead()
          .transform(utf8.decoder)
          .transform(const LineSplitter())
          .listen(
        _processPipeMessage,
        onError: (error) {
          print('Named Pipe error: $error');
          // 재연결 시도
          Future.delayed(const Duration(seconds: 5), _connectToNamedPipe);
        },
        onDone: () {
          print('Named Pipe disconnected');
          // 재연결 시도
          Future.delayed(const Duration(seconds: 5), _connectToNamedPipe);
        },
      );

      print('Connected to Named Pipe: $pipePath');
    } catch (e) {
      print('Failed to connect to Named Pipe: $e');
      // 재연결 시도
      Future.delayed(const Duration(seconds: 5), _connectToNamedPipe);
    }
  }

  static void _processPipeMessage(String message) {
    print('[PIPE] $message');

    // 모든 WebSocket 클라이언트에 전송
    for (final client in _clients) {
      try {
        client.sink.add(message);
      } catch (e) {
        print('Error sending to client: $e');
      }
    }
  }

  static void _handleWebSocket(WebSocket webSocket) {
    final channel = IOWebSocketChannel(webSocket);
    _clients.add(channel);

    print('WebSocket client connected. Total clients: ${_clients.length}');

    // 연결 환영 메시지
    channel.sink.add(json.encode({
      'type': 'connected',
      'message': 'Connected to Named Pipe server',
      'timestamp': DateTime.now().toIso8601String(),
    }));

    // 클라이언트 메시지 처리
    channel.stream.listen(
      (message) {
        print('Received from client: $message');
      },
      onDone: () {
        _clients.remove(channel);
        print(
            'WebSocket client disconnected. Total clients: ${_clients.length}');
      },
      onError: (error) {
        print('WebSocket error: $error');
        _clients.remove(channel);
      },
    );
  }

  static Future<void> stop() async {
    await _pipeSubscription?.cancel();
    await _httpServer?.close();

    for (final client in _clients) {
      await client.sink.close();
    }
    _clients.clear();

    print('Server stopped');
  }
}
