// lib/server/pipe_server.dart
import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:web_socket_channel/io.dart';

class PipeServer {
  static HttpServer? _httpServer;
  static final List<IOWebSocketChannel> _clients = [];
  static File? _pipeFile;
  static StreamSubscription? _pipeSubscription;

  // 통계 정보
  static int _totalMessagesReceived = 0;
  static int _totalSignalsDetected = 0;
  static int _totalClientsConnected = 0;
  static DateTime? _serverStartTime;
  static DateTime? _lastSignalTime;

  static Future<void> start() async {
    print('Starting Named Pipe server...');
    _serverStartTime = DateTime.now();

    // HTTP/WebSocket 서버 시작
    _httpServer = await HttpServer.bind('localhost', 8080);
    print('Server listening on ws://localhost:8080');

    // Named Pipe 연결
    await _connectToNamedPipe();

    // WebSocket 연결 처리
    _httpServer!.listen((HttpRequest request) {
      if (request.uri.path == '/pipe' &&
          WebSocketTransformer.isUpgradeRequest(request)) {
        WebSocketTransformer.upgrade(request).then(_handleWebSocket);
      } else if (request.uri.path == '/stats') {
        _handleStatsRequest(request);
      } else {
        request.response
          ..statusCode = HttpStatus.notFound
          ..write('Not found')
          ..close();
      }
    });
  }

  static Future<void> _connectToNamedPipe() async {
    try {
      const pipePath = r'\\.\pipe\RealtimeCaptureLogPipe';
      _pipeFile = File(pipePath);

      // Named Pipe 읽기 스트림
      _pipeSubscription = _pipeFile!
          .openRead()
          .transform(utf8.decoder)
          .transform(const LineSplitter())
          .listen(
        _processPipeMessage,
        onError: (error) {
          print('Named Pipe error: $error');
          // 재연결 시도
          Future.delayed(const Duration(seconds: 5), _connectToNamedPipe);
        },
        onDone: () {
          print('Named Pipe disconnected');
          // 재연결 시도
          Future.delayed(const Duration(seconds: 5), _connectToNamedPipe);
        },
      );

      print('Connected to Named Pipe: $pipePath');
    } catch (e) {
      print('Failed to connect to Named Pipe: $e');
      // 재연결 시도
      Future.delayed(const Duration(seconds: 5), _connectToNamedPipe);
    }
  }

  static void _processPipeMessage(String message) {
    print('[PIPE] $message');
    _totalMessagesReceived++;

    // 무시할 메시지인지 확인
    if (_shouldIgnoreMessage(message)) {
      return;
    }

    // 신호 패턴 검사 및 처리
    final processedMessage = _processSignalMessage(message);

    // 신호가 감지된 경우 통계 업데이트
    if (processedMessage != null && processedMessage['type'] == 'signal') {
      _totalSignalsDetected++;
      _lastSignalTime = DateTime.now();
    }

    // 모든 WebSocket 클라이언트에 전송
    for (final client in _clients) {
      try {
        if (processedMessage != null) {
          // 신호가 감지된 경우 JSON 형태로 전송
          client.sink.add(jsonEncode(processedMessage));
        } else {
          // 일반 로그 메시지는 원본 그대로 전송
          client.sink.add(message);
        }
      } catch (e) {
        print('Error sending to client: $e');
        _clients.remove(client);
      }
    }
  }

  // 신호 메시지 처리
  static Map<String, dynamic>? _processSignalMessage(String message) {
    // 유효한 신호 패턴 확인
    if (_isValidSignalMessage(message)) {
      final signalType = _extractSignalType(message);
      if (signalType != null) {
        return {
          'type': 'signal',
          'signalType': signalType,
          'timestamp': DateTime.now().toIso8601String(),
          'originalMessage': message,
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
        };
      }
    }

    // 시스템 메시지 처리
    if (_isSystemMessage(message)) {
      return {
        'type': 'system',
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }

    return null;
  }

  // 유효한 신호 메시지인지 확인
  static bool _isValidSignalMessage(String message) {
    const validPatterns = [
      '롱 신호가 감지되었습니다',
      '숏 신호가 감지되었습니다',
      'Long signal detected',
      'Short signal detected',
    ];

    return validPatterns.any((pattern) => message.contains(pattern));
  }

  // 무시할 메시지 패턴 확인
  static bool _shouldIgnoreMessage(String message) {
    const ignorePatterns = [
      'No specific signal detected',
      '[DEBUG]',
      'Frame logging',
      'disposed successfully',
      'Monitoring started',
      'Monitoring stopped',
    ];

    return ignorePatterns.any((pattern) => message.contains(pattern));
  }

  // 시스템 메시지 확인
  static bool _isSystemMessage(String message) {
    const systemPatterns = [
      '[GUI] 모니터링 시작',
      '영역 선택:',
      'Connected to',
      'Disconnected from',
    ];

    return systemPatterns.any((pattern) => message.contains(pattern));
  }

  // 신호 타입 추출
  static String? _extractSignalType(String message) {
    if (message.contains('롱') || message.contains('Long')) {
      return 'long';
    } else if (message.contains('숏') || message.contains('Short')) {
      return 'short';
    }
    return null;
  }

  static void _handleWebSocket(WebSocket webSocket) {
    final channel = IOWebSocketChannel(webSocket);
    _clients.add(channel);
    _totalClientsConnected++;

    print('WebSocket client connected. Total clients: ${_clients.length}');

    // 연결 환영 메시지
    channel.sink.add(jsonEncode({
      'type': 'connected',
      'message': 'Connected to Named Pipe server',
      'timestamp': DateTime.now().toIso8601String(),
      'stats': _getServerStats(),
    }));

    // 클라이언트 메시지 처리
    channel.stream.listen(
      (message) {
        print('Received from client: $message');
        _handleClientMessage(channel, message);
      },
      onDone: () {
        _clients.remove(channel);
        print(
            'WebSocket client disconnected. Total clients: ${_clients.length}');
      },
      onError: (error) {
        print('WebSocket error: $error');
        _clients.remove(channel);
      },
    );
  }

  // 클라이언트 메시지 처리
  static void _handleClientMessage(
      IOWebSocketChannel channel, dynamic message) {
    try {
      final data = jsonDecode(message as String);

      switch (data['type']) {
        case 'ping':
          channel.sink.add(jsonEncode({
            'type': 'pong',
            'timestamp': DateTime.now().toIso8601String(),
          }));
          break;
        case 'getStats':
          channel.sink.add(jsonEncode({
            'type': 'stats',
            'data': _getServerStats(),
            'timestamp': DateTime.now().toIso8601String(),
          }));
          break;
        default:
          print('Unknown message type: ${data['type']}');
      }
    } catch (e) {
      print('Error processing client message: $e');
    }
  }

  // 통계 정보 요청 처리
  static void _handleStatsRequest(HttpRequest request) {
    final stats = _getServerStats();

    request.response
      ..headers.contentType = ContentType.json
      ..write(jsonEncode(stats))
      ..close();
  }

  // 서버 통계 정보 반환
  static Map<String, dynamic> _getServerStats() {
    final uptime = _serverStartTime != null
        ? DateTime.now().difference(_serverStartTime!).inSeconds
        : 0;

    return {
      'serverStartTime': _serverStartTime?.toIso8601String(),
      'uptime': uptime,
      'totalMessagesReceived': _totalMessagesReceived,
      'totalSignalsDetected': _totalSignalsDetected,
      'totalClientsConnected': _totalClientsConnected,
      'currentClients': _clients.length,
      'lastSignalTime': _lastSignalTime?.toIso8601String(),
      'pipeConnected': _pipeSubscription != null,
    };
  }

  static Future<void> stop() async {
    await _pipeSubscription?.cancel();
    await _httpServer?.close();

    for (final client in _clients) {
      await client.sink.close();
    }
    _clients.clear();

    print('Server stopped');
  }
}
