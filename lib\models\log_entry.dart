// lib/models/log_entry.dart

// 로그 엔트리 모델
class LogEntry {
  final String message;
  final DateTime timestamp;
  final LogLevel level;
  final Map<String, dynamic>? metadata;

  LogEntry({
    required this.message,
    required this.timestamp,
    required this.level,
    this.metadata,
  });

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      level: LogLevel.values.firstWhere(
        (e) => e.toString().split('.').last == json['level'],
        orElse: () => LogLevel.info,
      ),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() => {
        'message': message,
        'timestamp': timestamp.toIso8601String(),
        'level': level.toString().split('.').last,
        'metadata': metadata,
      };
}

enum LogLevel {
  info,
  warning,
  error,
  success,
  signal,
  trade,
}
